import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnServiceTypeLongNameFromId } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { AdditionalChargeSubtotal } from '@/interface-models/Generic/Accounting/JobAccountingTotals/AdditionalChargeSubtotal';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface FreightAdjustmentLineItems {
  name: string;
  rate: string;
  amount: string;
}
interface PudLineItem {
  suburb: string;
  reference: string;
}

interface OutsideMetroCharge {
  exists: boolean;
  rate: string;
  total: string;
  gst: string;
}

interface FuelLevy {
  rate: string;
  total: string;
}

interface OverallAdjustmentLineItem {
  rate: string;
  name: string;
  total: string;
}
interface JobTotalSummary {
  jobId: number;
  reference: string;
  service: string;
  date: string;
  start: string;
  end: string;
  units: string;
  clientRate: any;
  freight: string;
  freightGst: string;
  puds: PudLineItem[];
  freightAdjustments: FreightAdjustmentLineItems[];
  outsideMetroCharge: OutsideMetroCharge;
  freightSubtotal: string;
  freightGstSubtotal: string;
  tolls: string;
  tollsGst: string;
  fuelLevy: FuelLevy;
  fuelLevyGst: string;
  overallAdjustments: OverallAdjustmentLineItem[];
  jobSubtotal: string;
  jobSubtotalGst: string;
  gst: string;
  jobTotal: string;
}

@Component({
  components: {},
})
export default class ClientAccessJobTotals extends Vue {
  @Prop() public jobDetails: JobDetails;

  public rowHeight = 24;

  get jobTotalsSummary(): JobTotalSummary | string {
    const x = this.jobDetails;
    if (x.jobId === undefined) {
      return '-';
    }
    const jobEpoch = x.pudItems && x.pudItems[0] ? x.pudItems[0].epochTime : 0;
    const jobDate = jobEpoch ? returnFormattedDate(jobEpoch) : '';
    let rate: number | string = '';
    let units: string = '';
    const clientRateTypeId = x.accounting.clientRates[0].rate.rateTypeId;
    switch (clientRateTypeId) {
      case 1:
        rate = (
          x.accounting.clientRates[0].rate.rateTypeObject as TimeRateType
        ).rate.toFixed(2);

        const billedDuration =
          x.accounting.finishedJobData.clientDurations.readableBilledDuration;
        const breakDuration =
          x.accounting.finishedJobData.clientDurations.readableBreakDuration;

        if (breakDuration === '0m') {
          units = billedDuration;
        } else {
          units = breakDuration + ' brk = ' + billedDuration;
        }
        break;
      case 6:
        rate = (x.accounting.clientRates[0].rate.rateTypeObject as any).rate;
        units = 'Quoted Rate';
        break;
    }

    // service type short name
    const serviceTypeName = returnServiceTypeLongNameFromId(x.serviceTypeId);

    // pud line items
    const pudLineItems: PudLineItem[] = [];
    for (const pud of x.pudItems) {
      if (pud.legTypeFlag !== 'P' && pud.legTypeFlag !== 'D') {
        continue;
      } else {
        const pudReferences = pud.pickupReference.concat(pud.dropoffReference);
        let builtStr = '';
        pudReferences.forEach((ref: JobReferenceDetails, index: number) => {
          if (index === pudReferences.length - 1) {
            builtStr += ref.reference;
          } else {
            builtStr += ref.reference + ', ';
          }
        });

        pudLineItems.push({
          suburb: pud.address.suburb,
          reference: builtStr,
        });
      }
    }

    // // freight adjustments
    // const additionalChargeLineItems: FreightAdjustmentLineItems[] = [];
    const additionalCharges: AdditionalChargeSubtotal[] =
      x.accounting.totals.subtotals.additionalChargeItems;
    // // find index of tolls charge items

    // if (additionalCharges && additionalCharges.length > 0) {
    //   for (const subtotal of additionalCharges) {
    //     const quantity = subtotal.items?.length ?? 0;

    //     additionalChargeLineItems.push({
    //       name: subtotal.longName + ' (' + quantity + ')',
    //       rate: subtotal.total.client.toFixed(2),
    //       amount: subtotal.total.client.toFixed(2),
    //     });
    //   }
    // }

    // outside metro
    const outsideMetroRateExists: boolean =
      x.accounting.totals.subtotals.outsideMetroChargeTotals.client !==
        undefined &&
      x.accounting.totals.subtotals.outsideMetroChargeTotals.client !== null &&
      x.accounting.totals.subtotals.outsideMetroChargeTotals.client > 0;

    // overall adjustments
    const overallAdjustmentLineItems: OverallAdjustmentLineItem[] = [];
    // find index of tolls charge items
    const overallAdjustments = additionalCharges.filter(
      (a) => a.longName !== 'Tolls',
    );

    if (overallAdjustments && overallAdjustments.length > 0) {
      for (const adjustment of overallAdjustments) {
        const quantity = adjustment?.items?.length || 0;

        overallAdjustmentLineItems.push({
          rate: '',
          name: adjustment.longName + ' (' + quantity + ')',
          total: adjustment.total.client.toFixed(2),
        });
      }
    }

    let jobReference = '';
    if (x.jobReference && x.jobReference.length > 0) {
      for (let i = 0; i < x.jobReference.length; i++) {
        if (i === x.jobReference.length - 1) {
          jobReference += x.jobReference[i].reference;
        } else if (x.jobReference[i].reference !== '') {
          jobReference += x.jobReference[i].reference + ', ';
        }
      }
    }

    return {
      jobId: x.jobId,
      reference: jobReference,
      service: serviceTypeName,
      date: jobDate,
      start: x.accounting.finishedJobData.clientDurations.readableStartTime,
      end: x.accounting.finishedJobData.clientDurations.readableEndTime,
      units,
      clientRate: rate,
      freight: x.accounting.totals.subtotals.freightCharges.client.toFixed(2),
      freightGst:
        x.accounting.totals.subtotals.freightGstCharges.client.toFixed(2),
      puds: pudLineItems,
      freightAdjustments: [],
      outsideMetroCharge: {
        exists: outsideMetroRateExists,
        rate: outsideMetroRateExists
          ? x.accounting.clientRates[0].outsideMetroRate + '%'
          : '',
        total: outsideMetroRateExists
          ? x.accounting.totals.subtotals.outsideMetroChargeTotals.client.toFixed(
              2,
            )
          : '',
        gst: outsideMetroRateExists
          ? x.accounting.totals.subtotals.outsideMetroChargeGstTotals.client.toFixed(
              2,
            )
          : '',
      },
      freightSubtotal:
        x.accounting.totals.subtotals.freightChargeTotals.client.toFixed(2),
      freightGstSubtotal:
        x.accounting.totals.subtotals.freightChargeGstTotals.client.toFixed(2),
      tolls:
        x.accounting.totals.subtotals.tollCharges.tollCharges.client.toFixed(2),
      tollsGst:
        x.accounting.totals.subtotals.tollCharges.tollGstCharges.client.toFixed(
          2,
        ),
      fuelLevy: {
        rate: x.accounting.additionalCharges.clientFuelSurcharge
          ? x.accounting.additionalCharges.clientFuelSurcharge
              .fuelSurchargeRate + '%'
          : '',
        total: x.accounting.totals.subtotals.fuelSurcharges.client
          ? x.accounting.totals.subtotals.fuelSurcharges.client.toFixed(2)
          : '',
      },
      fuelLevyGst: x.accounting.totals.subtotals.fuelGstSurcharges.client
        ? x.accounting.totals.subtotals.fuelGstSurcharges.client.toFixed(2)
        : '',
      overallAdjustments: overallAdjustmentLineItems,
      jobSubtotal: x.accounting.totals.subtotals.lessGst.client.toFixed(2),
      jobSubtotalGst:
        x.accounting.totals.subtotals.gstCharges.client.toFixed(2),
      gst: x.accounting.totals.subtotals.gstCharges.client.toFixed(2),
      jobTotal: x.accounting.totals.finalTotal.client.toFixed(2),
    };
  }
}

<template>
  <div :class="!clientId ? 'division-details-key-information' : ''">
    <v-layout class="top-panel">
      <GTitle
        title="Service Rate Variations"
        subtitle="Manage client-specific service rate variations."
        :divider="false"
        v-if="!clientId"
        class="ml-4"
      />
    </v-layout>
    <!-- Header Section -->
    <v-layout row wrap align-center class="ma-2">
      <!-- Title -->
      <v-flex class="d-flex align-center" v-if="!props.clientId">
        <v-text-field
          v-model="search"
          append-icon="search"
          label="Search Clients"
          solo
          flat
          class="v-solo-custom"
          clearable
          dense
          hide-details
          single-line
        ></v-text-field>
      </v-flex>

      <v-flex class="d-flex align-center ml-2" v-if="clientId">
        <GTitle
          title="Service Rate Variations"
          subtitle="Manage client-specific service rate variations."
          :divider="false"
        />
      </v-flex>

      <!-- Filter label (only show for division-level view) -->
      <v-flex class="d-flex align-center">
        <h6 class="subheader--faded mb-0 mr-2">Filter:</h6>
      </v-flex>
      <!-- Filter select (only show for division-level view) -->
      <v-flex class="d-flex align-center mr-4">
        <v-select
          v-model="selectedStatus"
          :items="statusFilterOptions"
          item-text="text"
          item-value="value"
          label="Filter by Status"
          solo
          flat
          dense
          hide-details
          single-line
          class="v-solo-custom"
          @change="loadRateVariations"
        />
      </v-flex>
      <v-spacer></v-spacer>

      <!-- Bulk Edit Button -->
      <v-flex class="d-flex align-center ml-2">
        <v-btn
          color="warning"
          @click="showBulkEditDialog = true"
          :disabled="!selectedVariations.length"
        >
          <v-icon size="14" class="mr-2">fas fa-edit</v-icon>
          <span>Bulk Edit ({{ selectedVariations.length }})</span>
        </v-btn>
      </v-flex>
      <!-- Add Button - Single button for both client and division level -->
      <v-flex class="d-flex align-center">
        <v-btn color="primary" @click="openRateVariationDialog">
          <v-icon size="14" class="mr-2">fas fa-plus</v-icon>
          <span>Add Rate Variation</span>
        </v-btn>
      </v-flex>
    </v-layout>
    <v-divider class="mb-4"></v-divider>
    <!-- Rate Variations Display Table -->
    <v-layout md12 class="body-scrollable--80 body-min-height--75">
      <div v-if="groupedVariations.length > 0">
        <div
          v-for="group in groupedVariations"
          :key="group.dateRange"
          class="mb-4"
        >
          <div class="table-group-header">
            <h5>{{ group.dateRange }}</h5>
          </div>
          <v-layout class="service-rate-variations-table">
            <div class="table-header-left-action-icon-container">
              <v-checkbox
                @click.stop
                @change="toggleGroupSelection(group.variations, $event)"
                hide-details
                :ripple="false"
                :disabled="group.variations.length === 0 || isLoading"
                color="info"
                class="ml-1"
              ></v-checkbox>
            </div>
            <v-data-table
              :headers="tableHeaders"
              :items="group.variations"
              :loading="isLoading"
              class="default-table-dark gd-dark-theme"
              hide-actions
              item-key="_id"
            >
              <template v-slot:items="tableProps">
                <tr>
                  <td class="checkbox-type-cell">
                    <v-checkbox
                      @click.stop
                      v-model="tableProps.item.isSelected"
                      hide-details
                      color="info"
                      class="mt-0"
                      :ripple="false"
                    ></v-checkbox>
                  </td>
                  <!-- Clients column - only show when no clientId prop (division level) -->
                  <td v-if="!props.clientId" class="client-type-cell">
                    <v-tooltip
                      bottom
                      v-if="tableProps.item.applyToIds.length > 1"
                    >
                      <template v-slot:activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on">
                          {{ getClientNames(tableProps.item.applyToIds) }}
                        </span>
                      </template>
                      <span>{{
                        getClientNames(tableProps.item.applyToIds, true)
                      }}</span>
                    </v-tooltip>
                    <span v-else>
                      {{ getClientNames(tableProps.item.applyToIds, true) }}
                    </span>
                  </td>
                  <td>
                    {{ getServiceTypeName(tableProps.item.serviceTypeId) }}
                  </td>
                  <td>{{ getRateTypeName(tableProps.item.rateTypeId) }}</td>
                  <td>
                    <span
                      :class="
                        getPercentageClass(
                          tableProps.item.clientAdjustmentPercentage,
                        )
                      "
                    >
                      {{
                        formatPercentage(
                          tableProps.item.clientAdjustmentPercentage,
                        )
                      }}
                    </span>
                  </td>
                  <td>
                    <span
                      :class="
                        getPercentageClass(
                          tableProps.item.fleetAssetAdjustmentPercentage,
                        )
                      "
                    >
                      {{
                        formatPercentage(
                          tableProps.item.fleetAssetAdjustmentPercentage,
                        )
                      }}
                    </span>
                  </td>
                  <td>{{ formatDateRange(tableProps.item) }}</td>
                  <td>
                    <span
                      class="status-cell"
                      :class="getStatusText(tableProps.item)"
                    >
                      {{ getStatusText(tableProps.item) }}
                    </span>
                  </td>
                  <td>
                    <!-- Edit button - only show if can edit -->
                    <div
                      class="action-buttons"
                      v-if="canEditVariation(tableProps.item)"
                    >
                      <v-btn icon small @click="editVariation(tableProps.item)">
                        <v-icon small color="accent">fas fa-edit</v-icon>
                      </v-btn>
                    </div>

                    <!-- Multi-client message - show when editing is disabled -->
                    <div v-else class="multi-client-message">
                      <v-menu bottom>
                        <template v-slot:activator="{ on: menu }">
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on: tooltip }">
                              <v-btn flat icon v-on="{ ...tooltip, ...menu }">
                                <v-icon color="accent" size="16"
                                  >fas fa-ellipsis-h</v-icon
                                >
                              </v-btn>
                            </template>
                            <span>View Actions</span>
                          </v-tooltip>
                        </template>
                        <v-list dense class="v-list-custom">
                          <v-list-tile
                            @click="navigateToDivisionRateVariations"
                          >
                            <v-list-tile-title>
                              <v-icon
                                small
                                size="10"
                                color="accent"
                                class="mr-2"
                                >fas fa-edit</v-icon
                              >
                              Includes more than one client, Edit at division
                              level
                            </v-list-tile-title>
                          </v-list-tile>
                          <v-list-tile
                            @click="removeClientFromVariation(tableProps.item)"
                          >
                            <v-list-tile-title class="text--secondary">
                              <v-icon size="10" small color="error" class="mr-2"
                                >fas fa-trash</v-icon
                              >
                              Remove current client from rate variation
                            </v-list-tile-title>
                          </v-list-tile>
                        </v-list>
                      </v-menu>
                    </div>
                  </td>
                </tr>
              </template>

              <template v-slot:no-data>
                <v-alert :value="true" color="info" icon="info">
                  No service rate variations found for this date range.
                </v-alert>
              </template>
            </v-data-table>
          </v-layout>
        </div>
      </div>
      <!-- Empty State -->
      <v-flex v-else class="text-xs-center pa-4">
        <h5>No Service Rate Variations</h5>
      </v-flex>
    </v-layout>
    <!-- Add/Edit Rate Variation Dialog with Tabs -->
    <v-dialog
      v-model="showAddForm"
      width="800px"
      max-width="800px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>{{ getDialogTitle() }}</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="cancelForm"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>

        <!-- Tabs for client details context -->
        <v-tabs
          v-if="props.clientId && !isEditing"
          v-model="activeTab"
          dark
          tabs
          centered
          color="#24232a"
          slider-color="yellow"
        >
          <v-tab>Create New</v-tab>
          <v-tab>Add to Existing</v-tab>
        </v-tabs>

        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <!-- Tab Items for client details context -->
            <v-tabs-items
              v-if="props.clientId && !isEditing"
              v-model="activeTab"
            >
              <!-- Create New Tab -->
              <v-tab-item>
                <v-layout row wrap class="tab-content-container" pa-3>
                  <v-flex md12>
                    <v-layout pb-2 pt-1>
                      <h3 class="subheader--light pb-2">
                        Create New Rate Variation
                      </h3>
                    </v-layout>
                    <v-layout row wrap>
                      <!-- CLIENT SELECT -->
                      <v-flex md12 class="mx-2 mb-4" v-if="!props.clientId">
                        <v-flex md4>
                          <v-layout
                            align-center
                            class="form-field-label-container"
                          >
                            <h6 class="pr-3 pb-0 form-field-required-marker">
                              Apply To Clients:
                            </h6>
                          </v-layout>
                        </v-flex>
                        <v-flex md12>
                          <v-combobox
                            v-model="selectedClientIds"
                            :items="availableClients"
                            item-text="clientDisplayName"
                            item-value="clientId"
                            label="Select Clients"
                            multiple
                            chips
                            deletable-chips
                            :rules="[validate.required]"
                            :disabled="formDisabled"
                            solo
                            flat
                            class="v-solo-custom"
                            hint="Select one or more clients to apply this rate variation to"
                            persistent-hint
                          ></v-combobox>
                        </v-flex>
                      </v-flex>

                      <!-- SERVICE AND RATE TYPE SELECT -->
                      <v-layout md12 class="mb-4">
                        <v-flex md6 class="mx-2">
                          <v-flex>
                            <v-layout
                              align-center
                              class="form-field-label-container"
                            >
                              <h6 class="pr-3 pb-0 form-field-required-marker">
                                Select Service Type:
                              </h6>
                              <v-spacer />
                              <v-checkbox
                                v-model="applyToAllServiceTypes"
                                label="Apply to all"
                                :disabled="formDisabled"
                                color="primary"
                                @change="onApplyToAllServiceTypesChange"
                              ></v-checkbox>
                            </v-layout>
                          </v-flex>
                          <v-flex>
                            <v-select
                              v-model="newVariation.serviceTypeId"
                              :items="availableServiceTypes"
                              item-text="longServiceTypeName"
                              item-value="serviceTypeId"
                              label="Service Type"
                              :disabled="formDisabled || applyToAllServiceTypes"
                              clearable
                              solo
                              flat
                              class="v-solo-custom"
                              hide-details
                            ></v-select>
                          </v-flex>
                        </v-flex>
                        <v-flex md6 class="mx-2">
                          <v-flex>
                            <v-layout
                              align-center
                              class="form-field-label-container"
                            >
                              <h6 class="pr-3 pb-0 form-field-required-marker">
                                Select Rate Type:
                              </h6>
                              <v-spacer />
                              <v-checkbox
                                v-model="applyToAllRateTypes"
                                label="Apply to all"
                                :disabled="formDisabled"
                                color="primary"
                                @change="onApplyToAllRateTypesChange"
                              ></v-checkbox>
                            </v-layout>
                          </v-flex>
                          <v-flex>
                            <v-select
                              v-model="newVariation.rateTypeId"
                              :items="availableRateTypes"
                              item-text="longName"
                              item-value="rateTypeId"
                              label="Rate Type"
                              :disabled="formDisabled || applyToAllRateTypes"
                              clearable
                              solo
                              flat
                              class="v-solo-custom"
                              hide-details
                            ></v-select>
                          </v-flex>
                        </v-flex>
                      </v-layout>

                      <!-- DATE SELECT -->
                      <v-layout md12 class="mb-4">
                        <v-flex md6 class="mx-2">
                          <v-flex>
                            <v-layout
                              align-center
                              class="form-field-label-container"
                            >
                              <h6 class="pr-3 pb-0 form-field-required-marker">
                                Valid From Date:
                              </h6>
                            </v-layout>
                          </v-flex>
                          <v-flex>
                            <DatePickerBasic
                              :epochTime="newVariation.validFromDate"
                              @setEpoch="setValidFromDate"
                              labelName="Valid From Date"
                              :isRequired="true"
                              :validate="validate"
                              :soloInput="true"
                              :formDisabled="formDisabled"
                              :hide-details="true"
                            />
                          </v-flex>
                        </v-flex>
                        <v-flex md6 class="mx-2">
                          <v-flex>
                            <v-layout
                              align-center
                              class="form-field-label-container"
                            >
                              <h6 class="pr-3 pb-0 form-field-required-marker">
                                Valid To Date:
                              </h6>
                            </v-layout>
                          </v-flex>
                          <v-flex>
                            <DatePickerBasic
                              :epochTime="newVariation.validToDate"
                              @setEpoch="setValidToDate"
                              labelName="Valid To Date"
                              :validate="validate"
                              :soloInput="true"
                              :formDisabled="formDisabled"
                              :hide-details="true"
                            />
                          </v-flex>
                        </v-flex>
                      </v-layout>

                      <!-- ADJUSTMENT PERCENT -->
                      <v-layout md12 class="mb-4">
                        <v-flex md6 class="mx-2">
                          <v-flex>
                            <v-layout
                              align-center
                              class="form-field-label-container"
                            >
                              <h6 class="pr-3 pb-0 form-field-required-marker">
                                Client Adjustment %:
                              </h6>
                            </v-layout>
                          </v-flex>
                          <v-flex>
                            <v-text-field
                              v-model.number="
                                newVariation.clientAdjustmentPercentage
                              "
                              label="Client Adjustment (%)"
                              type="number"
                              step="1.0"
                              :disabled="formDisabled"
                              suffix="%"
                              solo
                              flat
                              class="v-solo-custom"
                              hint="Positive values increase rates, negative values decrease rates"
                              persistent-hint
                            ></v-text-field>
                          </v-flex>
                        </v-flex>
                        <v-flex md6 class="mx-2">
                          <v-flex>
                            <v-layout
                              align-center
                              class="form-field-label-container"
                            >
                              <h6 class="pr-3 pb-0 form-field-required-marker">
                                Fleet Adjustment %:
                              </h6>
                            </v-layout>
                          </v-flex>
                          <v-flex>
                            <v-text-field
                              v-model.number="
                                newVariation.fleetAssetAdjustmentPercentage
                              "
                              label="Fleet Asset Adjustment (%)"
                              type="number"
                              step="1.0"
                              :disabled="formDisabled"
                              suffix="%"
                              solo
                              flat
                              class="v-solo-custom"
                              hint="Positive values increase rates, negative values decrease rates"
                              persistent-hint
                            ></v-text-field>
                          </v-flex>
                        </v-flex>
                      </v-layout>
                    </v-layout>
                  </v-flex>
                </v-layout>
                <v-flex md12 mt-4>
                  <v-divider></v-divider>
                  <v-layout align-center pa-2>
                    <v-btn
                      outline
                      color="error"
                      @click="cancelForm"
                      :disabled="formDisabled"
                      >Cancel</v-btn
                    >
                    <v-spacer></v-spacer>
                    <v-btn
                      color="primary"
                      @click="saveRateVariation"
                      :disabled="formDisabled"
                      :loading="isSaving"
                      solo
                      block
                    >
                      {{ isEditing ? 'Update' : 'Save' }}
                    </v-btn>
                  </v-layout>
                </v-flex>
              </v-tab-item>

              <!-- Add to Existing Tab -->
              <v-tab-item>
                <v-layout row wrap class="tab-content-container" pa-3>
                  <v-flex md12>
                    <v-layout pb-2 pt-1>
                      <h3 class="subheader--light pb-2">
                        Add Client to Existing Rate Variations
                      </h3>
                    </v-layout>
                    <h4 class="mb-3">
                      Choose one or more existing variations. The current client
                      will be added to their client list.
                    </h4>

                    <!-- Existing Variations List -->
                    <div v-if="existingVariationsForSelection.length > 0">
                      <v-layout>
                        <v-data-table
                          :headers="existingVariationsTableHeaders"
                          :items="existingVariationsForSelection"
                          :loading="isLoading"
                          class="default-table-dark gd-dark-theme"
                          hide-actions
                          item-key="_id"
                        >
                          <template v-slot:items="tableProps">
                            <tr>
                              <td class="checkbox-type-cell">
                                <v-checkbox
                                  @click.stop
                                  v-model="tableProps.item.isSelected"
                                  hide-details
                                  color="info"
                                  class="mt-0"
                                  :ripple="false"
                                ></v-checkbox>
                              </td>
                              <td>
                                {{
                                  getServiceTypeName(
                                    tableProps.item.serviceTypeId,
                                  )
                                }}
                              </td>
                              <td>
                                {{
                                  getRateTypeName(tableProps.item.rateTypeId)
                                }}
                              </td>
                              <td>
                                <span
                                  :class="
                                    getPercentageClass(
                                      tableProps.item
                                        .clientAdjustmentPercentage,
                                    )
                                  "
                                >
                                  {{
                                    formatPercentage(
                                      tableProps.item
                                        .clientAdjustmentPercentage,
                                    )
                                  }}
                                </span>
                              </td>
                              <td>
                                <span
                                  :class="
                                    getPercentageClass(
                                      tableProps.item
                                        .fleetAssetAdjustmentPercentage,
                                    )
                                  "
                                >
                                  {{
                                    formatPercentage(
                                      tableProps.item
                                        .fleetAssetAdjustmentPercentage,
                                    )
                                  }}
                                </span>
                              </td>
                              <td>{{ formatDateRange(tableProps.item) }}</td>
                              <td>
                                <span
                                  class="status-cell"
                                  :class="getStatusText(tableProps.item)"
                                >
                                  {{ getStatusText(tableProps.item) }}
                                </span>
                              </td>
                            </tr>
                          </template>

                          <template v-slot:no-data>
                            <v-alert :value="true" color="info" icon="info">
                              No existing rate variations found.
                            </v-alert>
                          </template>
                        </v-data-table>
                      </v-layout>
                    </div>

                    <!-- Empty State -->
                    <v-flex v-else class="text-xs-center pa-4">
                      <h5>No Existing Rate Variations Available</h5>
                      <p>
                        There are no existing rate variations to add this client
                        to.
                      </p>
                    </v-flex>
                  </v-flex>
                </v-layout>
                <v-flex md12 mt-4>
                  <v-divider></v-divider>
                  <v-layout align-center pa-2>
                    <v-btn
                      outline
                      color="error"
                      @click="cancelForm"
                      :disabled="isSaving"
                      >Cancel</v-btn
                    >
                    <v-spacer></v-spacer>
                    <v-btn
                      color="primary"
                      @click="addClientToSelectedVariations"
                      :disabled="
                        selectedExistingVariations.length === 0 || isSaving
                      "
                      :loading="isSaving"
                      solo
                      block
                    >
                      Add to
                      {{ selectedExistingVariations.length }} Variation(s)
                    </v-btn>
                  </v-layout>
                </v-flex>
              </v-tab-item>
            </v-tabs-items>

            <!-- Non-tabbed content for division level or editing -->
            <div v-else>
              <v-layout row wrap class="tab-content-container" pa-3>
                <v-flex md12>
                  <v-layout pb-2 pt-1>
                    <h3 class="subheader--light pb-2">
                      {{ isEditing ? 'Edit' : 'Add New' }} Rate Variation
                    </h3>
                  </v-layout>
                  <v-layout row wrap>
                    <!-- CLIENT SELECT -->
                    <v-flex md12 class="mx-2 mb-4" v-if="!props.clientId">
                      <v-flex md4>
                        <v-layout
                          align-center
                          class="form-field-label-container"
                        >
                          <h6 class="pr-3 pb-0 form-field-required-marker">
                            Apply To Clients:
                          </h6>
                        </v-layout>
                      </v-flex>
                      <v-flex md12>
                        <v-combobox
                          v-model="selectedClientIds"
                          :items="availableClients"
                          item-text="clientDisplayName"
                          item-value="clientId"
                          label="Select Clients"
                          multiple
                          chips
                          deletable-chips
                          :rules="[validate.required]"
                          :disabled="formDisabled"
                          solo
                          flat
                          class="v-solo-custom"
                          hint="Select one or more clients to apply this rate variation to"
                          persistent-hint
                        ></v-combobox>
                      </v-flex>
                    </v-flex>

                    <!-- SERVICE AND RATE TYPE SELECT -->
                    <v-layout md12 class="mb-4">
                      <v-flex md6 class="mx-2">
                        <v-flex>
                          <v-layout
                            align-center
                            class="form-field-label-container"
                          >
                            <h6 class="pr-3 pb-0 form-field-required-marker">
                              Select Service Type:
                            </h6>
                            <v-spacer />
                            <v-checkbox
                              v-model="applyToAllServiceTypes"
                              label="Apply to all"
                              :disabled="formDisabled"
                              color="primary"
                              @change="onApplyToAllServiceTypesChange"
                            ></v-checkbox>
                          </v-layout>
                        </v-flex>
                        <v-flex>
                          <v-select
                            v-model="newVariation.serviceTypeId"
                            :items="availableServiceTypes"
                            item-text="longServiceTypeName"
                            item-value="serviceTypeId"
                            label="Service Type"
                            :disabled="formDisabled || applyToAllServiceTypes"
                            clearable
                            solo
                            flat
                            class="v-solo-custom"
                            hide-details
                          ></v-select>
                        </v-flex>
                      </v-flex>
                      <v-flex md6 class="mx-2">
                        <v-flex>
                          <v-layout
                            align-center
                            class="form-field-label-container"
                          >
                            <h6 class="pr-3 pb-0 form-field-required-marker">
                              Select Rate Type:
                            </h6>
                            <v-spacer />
                            <v-checkbox
                              v-model="applyToAllRateTypes"
                              label="Apply to all"
                              :disabled="formDisabled"
                              color="primary"
                              @change="onApplyToAllRateTypesChange"
                            ></v-checkbox>
                          </v-layout>
                        </v-flex>
                        <v-flex>
                          <v-select
                            v-model="newVariation.rateTypeId"
                            :items="availableRateTypes"
                            item-text="longName"
                            item-value="rateTypeId"
                            label="Rate Type"
                            :disabled="formDisabled || applyToAllRateTypes"
                            clearable
                            solo
                            flat
                            class="v-solo-custom"
                            hide-details
                          ></v-select>
                        </v-flex>
                      </v-flex>
                    </v-layout>

                    <!-- DATE SELECT -->
                    <v-layout md12 class="mb-4">
                      <v-flex md6 class="mx-2">
                        <v-flex>
                          <v-layout
                            align-center
                            class="form-field-label-container"
                          >
                            <h6 class="pr-3 pb-0 form-field-required-marker">
                              Valid From Date:
                            </h6>
                          </v-layout>
                        </v-flex>
                        <v-flex>
                          <DatePickerBasic
                            :epochTime="newVariation.validFromDate"
                            @setEpoch="setValidFromDate"
                            labelName="Valid From Date"
                            :isRequired="true"
                            :validate="validate"
                            :soloInput="true"
                            :formDisabled="formDisabled"
                            :hide-details="true"
                          />
                        </v-flex>
                      </v-flex>
                      <v-flex md6 class="mx-2">
                        <v-flex>
                          <v-layout
                            align-center
                            class="form-field-label-container"
                          >
                            <h6 class="pr-3 pb-0 form-field-required-marker">
                              Valid To Date:
                            </h6>
                          </v-layout>
                        </v-flex>
                        <v-flex>
                          <DatePickerBasic
                            :epochTime="newVariation.validToDate"
                            @setEpoch="setValidToDate"
                            labelName="Valid To Date"
                            :validate="validate"
                            :soloInput="true"
                            :formDisabled="formDisabled"
                            :hide-details="true"
                          />
                        </v-flex>
                      </v-flex>
                    </v-layout>

                    <!-- ADJUSTMENT PERCENT -->
                    <v-layout md12 class="mb-4">
                      <v-flex md6 class="mx-2">
                        <v-flex>
                          <v-layout
                            align-center
                            class="form-field-label-container"
                          >
                            <h6 class="pr-3 pb-0 form-field-required-marker">
                              Client Adjustment %:
                            </h6>
                          </v-layout>
                        </v-flex>
                        <v-flex>
                          <v-text-field
                            v-model.number="
                              newVariation.clientAdjustmentPercentage
                            "
                            label="Client Adjustment (%)"
                            type="number"
                            step="1.0"
                            :disabled="formDisabled"
                            suffix="%"
                            solo
                            flat
                            class="v-solo-custom"
                            hint="Positive values increase rates, negative values decrease rates"
                            persistent-hint
                          ></v-text-field>
                        </v-flex>
                      </v-flex>
                      <v-flex md6 class="mx-2">
                        <v-flex>
                          <v-layout
                            align-center
                            class="form-field-label-container"
                          >
                            <h6 class="pr-3 pb-0 form-field-required-marker">
                              Fleet Adjustment %:
                            </h6>
                          </v-layout>
                        </v-flex>
                        <v-flex>
                          <v-text-field
                            v-model.number="
                              newVariation.fleetAssetAdjustmentPercentage
                            "
                            label="Fleet Asset Adjustment (%)"
                            type="number"
                            step="1.0"
                            :disabled="formDisabled"
                            suffix="%"
                            solo
                            flat
                            class="v-solo-custom"
                            hint="Positive values increase rates, negative values decrease rates"
                            persistent-hint
                          ></v-text-field>
                        </v-flex>
                      </v-flex>
                    </v-layout>
                  </v-layout>
                </v-flex>
              </v-layout>
              <v-flex md12 mt-4>
                <v-divider></v-divider>
                <v-layout align-center pa-2>
                  <v-btn
                    outline
                    color="error"
                    @click="cancelForm"
                    :disabled="formDisabled"
                    >Cancel</v-btn
                  >
                  <v-spacer></v-spacer>
                  <v-btn
                    color="primary"
                    @click="saveRateVariation"
                    :disabled="formDisabled"
                    :loading="isSaving"
                    solo
                    block
                  >
                    {{ isEditing ? 'Update' : 'Save' }}
                  </v-btn>
                </v-layout>
              </v-flex>
            </div>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>
    <!-- Bulk Edit Dialog -->
    <v-dialog
      v-model="showBulkEditDialog"
      width="600px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <div>
        <v-layout
          justify-space-between
          align-center
          class="task-bar app-theme__center-content--header no-highlight"
        >
          <span>Bulk Edit Rate Variations</span>
          <div
            class="app-theme__center-content--closebutton"
            @click="showBulkEditDialog = false"
          >
            <v-icon class="app-theme__center-content--closebutton--icon"
              >fal fa-times</v-icon
            >
          </div>
        </v-layout>

        <v-layout
          row
          wrap
          class="app-theme__center-content--body dialog-content"
        >
          <v-flex md12>
            <v-layout row wrap class="body-scrollable--65" pa-3>
              <v-flex md12>
                <v-layout pb-2 pt-1>
                  <h3 class="subheader--light pb-2">
                    Bulk Edit
                    {{ selectedVariations.length }} rate variation(s).
                  </h3>
                </v-layout>
                <h4 class="mb-3">
                  Leave fields empty to keep existing values.
                </h4>

                <v-layout row wrap>
                  <v-flex md12>
                    <v-layout>
                      <v-flex md4>
                        <v-layout
                          align-center
                          class="form-field-label-container"
                        >
                          <h6
                            class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >
                            Client Adjustment:
                          </h6>
                        </v-layout>
                      </v-flex>
                      <v-flex md8>
                        <v-text-field
                          v-model.number="bulkEdit.clientAdjustmentPercentage"
                          label="Client Adjustment (%)"
                          type="number"
                          step="0.01"
                          suffix="%"
                          solo
                          flat
                          class="v-solo-custom"
                          clearable
                        ></v-text-field>
                      </v-flex>
                    </v-layout>
                  </v-flex>

                  <v-flex md12>
                    <v-layout>
                      <v-flex md4>
                        <v-layout
                          align-center
                          class="form-field-label-container"
                        >
                          <h6
                            class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >
                            Fleet Adjustment:
                          </h6>
                        </v-layout>
                      </v-flex>
                      <v-flex md8>
                        <v-text-field
                          v-model.number="
                            bulkEdit.fleetAssetAdjustmentPercentage
                          "
                          label="Fleet Asset Adjustment (%)"
                          type="number"
                          step="0.01"
                          suffix="%"
                          solo
                          flat
                          class="v-solo-custom"
                          clearable
                        ></v-text-field>
                      </v-flex>
                    </v-layout>
                  </v-flex>

                  <v-flex md12>
                    <v-layout>
                      <v-flex md4>
                        <v-layout
                          align-center
                          class="form-field-label-container"
                        >
                          <h6
                            class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >
                            Start Date:
                          </h6>
                        </v-layout>
                      </v-flex>
                      <v-flex md8>
                        <DatePickerBasic
                          :epochTime="bulkEdit.validFromDate"
                          @setEpoch="setBulkValidFromDate"
                          labelName="Valid From Date"
                          :soloInput="true"
                        />
                      </v-flex>
                    </v-layout>
                  </v-flex>

                  <v-flex md12>
                    <v-layout>
                      <v-flex md4>
                        <v-layout
                          align-center
                          class="form-field-label-container"
                        >
                          <h6
                            class="subheader--faded pr-3 pb-0 form-field-required-marker"
                          >
                            End Date:
                          </h6>
                        </v-layout>
                      </v-flex>
                      <v-flex md8>
                        <DatePickerBasic
                          :epochTime="bulkEdit.validToDate"
                          @setEpoch="setBulkValidToDate"
                          labelName="Valid To Date"
                          :soloInput="true"
                        />
                      </v-flex>
                    </v-layout>
                  </v-flex>
                </v-layout>
              </v-flex>

              <v-flex md12 mt-4>
                <v-divider></v-divider>
                <v-layout align-center pa-2>
                  <v-btn
                    outline
                    color="error"
                    @click="showBulkEditDialog = false"
                    >Cancel</v-btn
                  >
                  <v-spacer></v-spacer>
                  <v-btn
                    color="primary"
                    @click="applyBulkEdit"
                    :loading="isSaving"
                  >
                    Apply Changes
                  </v-btn>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-flex>
        </v-layout>
      </div>
    </v-dialog>
    <!-- Smart Suggestion Dialog -->
    <v-dialog
      v-model="showSuggestionDialog"
      max-width="600px"
      class="ma-0"
      persistent
      no-click-animation
      content-class="v-dialog-custom"
    >
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Duplicate Variation</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="showSuggestionDialog = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12>
          <v-layout class="pa-2 mt-2">
            <v-icon color="warning" class="mr-2 ml-2">
              fas fa-exclamation-triangle
            </v-icon>
            <h3 class="subheader--light ml-3">Duplicate Variation Found</h3>
          </v-layout>
          <div class="suggestion-message ma-4">
            A rate variation with identical settings but different date range
            already exists. Would you like to edit the existing variation
            instead of creating a duplicate?
          </div>
        </v-flex>
        <v-layout row wrap class="body-scrollable--65" pa-3>
          <!-- Dialog Content -->
          <v-flex
            v-if="suggestionDialogExistingVariation"
            class="suggestion-dialog-content"
          >
            <h4 class="ma-2">Variation Comparison:</h4>

            <v-layout md12 column>
              <!-- Service Type Comparison -->
              <v-flex class="detail-item py-2 px-3 mb-1">
                <v-layout row align-center>
                  <v-flex xs4>
                    <span class="detail-label font-weight-medium"
                      >Service Type:</span
                    >
                  </v-flex>
                  <v-flex xs4>
                    <span class="detail-value">{{
                      suggestionDialogExistingVariation?.serviceTypeId
                        ? returnServiceTypeShortNameFromId(
                            suggestionDialogExistingVariation.serviceTypeId,
                            'Unknown Service',
                          )
                        : 'All Services'
                    }}</span>
                  </v-flex>
                  <v-flex xs4>
                    <span
                      class="detail-value"
                      :class="
                        suggestionDialogExistingVariation?.serviceTypeId !==
                        newVariation.serviceTypeId
                          ? 'primary--text font-weight-bold'
                          : ''
                      "
                    >
                      {{
                        newVariation.serviceTypeId
                          ? getServiceTypeName(newVariation.serviceTypeId)
                          : 'All Service Types'
                      }}
                    </span>
                  </v-flex>
                </v-layout>
              </v-flex>

              <!-- Rate Type Comparison -->
              <v-flex class="detail-item py-2 px-3 mb-1">
                <v-layout row align-center>
                  <v-flex xs4>
                    <span class="detail-label font-weight-medium"
                      >Rate Type:</span
                    >
                  </v-flex>
                  <v-flex xs4>
                    <span class="detail-value">{{
                      suggestionDialogExistingVariation?.rateTypeId
                        ? returnRateTypeLongNameFromId(
                            suggestionDialogExistingVariation.rateTypeId,
                            'Unknown Rate Type',
                          )
                        : 'All Rate Types'
                    }}</span>
                  </v-flex>
                  <v-flex xs4>
                    <span
                      class="detail-value"
                      :class="
                        suggestionDialogExistingVariation?.rateTypeId !==
                        newVariation.rateTypeId
                          ? 'primary--text font-weight-bold'
                          : ''
                      "
                    >
                      {{
                        newVariation.rateTypeId
                          ? getRateTypeName(newVariation.rateTypeId)
                          : 'All Rate Types'
                      }}
                    </span>
                  </v-flex>
                </v-layout>
              </v-flex>

              <!-- Client Adjustment Comparison -->
              <v-flex class="detail-item py-2 px-3 mb-1">
                <v-layout row align-center>
                  <v-flex xs4>
                    <span class="detail-label font-weight-medium"
                      >Client Adjustment:</span
                    >
                  </v-flex>
                  <v-flex xs4>
                    <span class="detail-value">{{
                      formatPercentage(
                        suggestionDialogExistingVariation?.clientAdjustmentPercentage,
                      )
                    }}</span>
                  </v-flex>
                  <v-flex xs4>
                    <span class="detail-value">
                      {{
                        formatPercentage(
                          newVariation.clientAdjustmentPercentage,
                        )
                      }}
                    </span>
                  </v-flex>
                </v-layout>
              </v-flex>

              <!-- Fleet Adjustment Comparison -->
              <v-flex class="detail-item py-2 px-3 mb-1">
                <v-layout row align-center>
                  <v-flex xs4>
                    <span class="detail-label font-weight-medium"
                      >Fleet Adjustment:</span
                    >
                  </v-flex>
                  <v-flex xs4>
                    <span class="detail-value">{{
                      formatPercentage(
                        suggestionDialogExistingVariation?.fleetAssetAdjustmentPercentage,
                      )
                    }}</span>
                  </v-flex>
                  <v-flex xs4>
                    <span class="detail-value">
                      {{
                        formatPercentage(
                          newVariation.fleetAssetAdjustmentPercentage,
                        )
                      }}
                    </span>
                  </v-flex>
                </v-layout>
              </v-flex>

              <!-- Date Range Comparison -->
              <v-flex class="detail-item py-2 px-3 mb-1">
                <v-layout row align-center>
                  <v-flex xs4>
                    <span class="detail-label font-weight-medium"
                      >Date Range:</span
                    >
                  </v-flex>
                  <v-flex xs4>
                    <span class="detail-value warning--text font-weight-bold">{{
                      suggestionDialogExistingVariation
                        ? formatDateRange(suggestionDialogExistingVariation)
                        : ''
                    }}</span>
                  </v-flex>
                  <v-flex xs4>
                    <span class="detail-value font-weight-bold">{{
                      formatDateRange(newVariation)
                    }}</span>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
            <v-divider class="ma-2"></v-divider>
          </v-flex>
          <v-layout md12 space-between>
            <v-btn outline color="error" @click="showSuggestionDialog = false"
              >Cancel</v-btn
            >
            <v-spacer></v-spacer>
            <v-btn color="primary" @click="suggestionDialogPrimaryAction">
              Edit Existing
            </v-btn>
          </v-layout>
        </v-layout>
      </v-layout>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import DatePickerBasic from '@/components/common/date-picker/date_picker_basic.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnRateTypeLongNameFromId,
  returnServiceTypeShortNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import {
  JobRateType,
  ServiceTypeRates,
  serviceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { ClientServiceRateVariationsStatus } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsStatus';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';
import moment from 'moment-timezone';
import {
  computed,
  onMounted,
  ref,
  watch,
  type ComputedRef,
  type Ref,
} from 'vue';
import { useRouter } from 'vue-router/composables';

// Extend the type to include selection properties
type ClientServiceRateVariationsWithSelection = ClientServiceRateVariations & {
  isSelected: boolean;
};

const props = withDefaults(
  defineProps<{
    clientId?: string;
  }>(),
  {
    clientId: undefined,
  },
);

const serviceRateVariationsStore = useServiceRateVariationsStore();
const companyDetailsStore = useCompanyDetailsStore();
const clientDetailsStore = useClientDetailsStore();

const router = useRouter();

const validate = validationRules;
const search: Ref<string> = ref('');

const suggestionDialogExistingVariation: Ref<ClientServiceRateVariations | null> =
  ref(null);
const suggestionDialogPrimaryAction = ref(() => {});
const showSuggestionDialog: Ref<boolean> = ref(false);

const isLoading: Ref<boolean> = ref(false);
const isSaving: Ref<boolean> = ref(false);
const showAddForm: Ref<boolean> = ref(false);
const isEditing: Ref<boolean> = ref(false);
const showBulkEditDialog: Ref<boolean> = ref(false);
const formDisabled: Ref<boolean> = ref(false);
const activeTab: Ref<number> = ref(0); // 0 for Create New, 1 for Add to Existing

// Add to existing variations
const existingVariationsForSelection: Ref<
  ClientServiceRateVariationsWithSelection[]
> = ref([]);
const selectedExistingVariations: Ref<string[]> = ref([]);

// Apply to all checkboxes
const applyToAllServiceTypes: Ref<boolean> = ref(false);
const applyToAllRateTypes: Ref<boolean> = ref(false);

// Client selection
const selectedClientIds: Ref<(string | ClientSearchSummary)[]> = ref([]);

// Status filter
const selectedStatus: Ref<ClientServiceRateVariationsStatus> = ref(
  ClientServiceRateVariationsStatus.ALL,
);

// Rate variations data
const rateVariations: Ref<ClientServiceRateVariationsWithSelection[]> = ref([]);
const selectedVariations: Ref<string[]> = ref([]);
const editingVariation: Ref<ClientServiceRateVariations | null> = ref(null);

const newVariation: Ref<ClientServiceRateVariations> = ref(
  new ClientServiceRateVariations(),
);

// Bulk edit data
const bulkEdit: Ref<Partial<ClientServiceRateVariations>> = ref({
  clientAdjustmentPercentage: null,
  fleetAssetAdjustmentPercentage: null,
  validFromDate: 0,
  validToDate: 0,
});

// Cache for all division variations to avoid redundant API calls
const allDivisionVariations: Ref<ClientServiceRateVariations[]> = ref([]);

const availableServiceTypes: ComputedRef<ServiceTypes[]> = computed(() => {
  return companyDetailsStore.getServiceTypesList.filter(
    (serviceType: ServiceTypes) => serviceType.divisionService,
  );
});

const availableRateTypes: ComputedRef<ServiceTypeRates[]> = computed(() => {
  return serviceTypeRates.filter((rateType) => !rateType.adhoc);
});

// Available clients from client store
const availableClients: ComputedRef<ClientSearchSummary[]> = computed(() => {
  return clientDetailsStore.clientSummaryList || [];
});

// Status filter options
const statusFilterOptions = computed(() => [
  { text: 'All', value: ClientServiceRateVariationsStatus.ALL },
  { text: 'Current', value: ClientServiceRateVariationsStatus.CURRENT },
  { text: 'Expired', value: ClientServiceRateVariationsStatus.EXPIRED },
]);

// Table headers - dynamic based on whether we're in client details or division level
const tableHeaders = computed(() => {
  const headers: TableHeader[] = [
    {
      text: '',
      value: 'select',
      sortable: false,
      class: 'service-rate-variations-checkbox-column-header',
      align: 'left',
      width: '80px',
    },
  ];

  // Only show clients column when no clientId prop (division level)
  if (!props.clientId) {
    headers.push({
      text: 'Clients',
      value: 'clients',
      sortable: false,
      align: 'left',
      width: '240px',
    });
  }

  // Add remaining headers
  headers.push(
    {
      text: 'Service Type',
      value: 'serviceTypeId',
      sortable: true,
      align: 'left',
    },
    { text: 'Rate Type', value: 'rateTypeId', sortable: true, align: 'left' },
    {
      text: 'Client Adj. (%)',
      value: 'clientAdjustmentPercentage',
      sortable: true,
      align: 'left',
    },
    {
      text: 'Fleet Adj. (%)',
      value: 'fleetAssetAdjustmentPercentage',
      sortable: true,
      align: 'left',
    },
    { text: 'Date Range', value: 'dateRange', sortable: false, align: 'left' },
    { text: 'Status', value: 'status', sortable: false, align: 'left' },
    { text: 'Actions', value: 'actions', sortable: false, align: 'left' },
  );

  return headers;
});

// Table headers for existing variations selection dialog
const existingVariationsTableHeaders = computed(() => {
  const headers: TableHeader[] = [
    {
      text: '',
      value: 'select',
      sortable: false,
      class: 'service-rate-variations-checkbox-column-header',
      align: 'left',
      width: '80px',
    },
    {
      text: 'Service Type',
      value: 'serviceTypeId',
      sortable: true,
      align: 'left',
    },
    { text: 'Rate Type', value: 'rateTypeId', sortable: true, align: 'left' },
    {
      text: 'Client Adj. (%)',
      value: 'clientAdjustmentPercentage',
      sortable: true,
      align: 'left',
    },
    {
      text: 'Fleet Adj. (%)',
      value: 'fleetAssetAdjustmentPercentage',
      sortable: true,
      align: 'left',
    },
    { text: 'Date Range', value: 'dateRange', sortable: false, align: 'left' },
    { text: 'Status', value: 'status', sortable: false, align: 'left' },
  ];

  return headers;
});

// Filtered variations based on search
const filteredRateVariations = computed(() => {
  if (!search.value || search.value.trim() === '') {
    return rateVariations.value;
  }

  const searchTerm = search.value.toLowerCase().trim();

  return rateVariations.value.filter((variation) => {
    // Search in client names
    const clientNames = getClientNames(
      variation.applyToIds,
      true,
    ).toLowerCase();
    if (clientNames.includes(searchTerm)) {
      return true;
    }

    // Search in service type name
    const serviceTypeName = getServiceTypeName(
      variation.serviceTypeId,
    ).toLowerCase();
    if (serviceTypeName.includes(searchTerm)) {
      return true;
    }

    // Search in rate type name
    const rateTypeName = getRateTypeName(variation.rateTypeId).toLowerCase();
    if (rateTypeName.includes(searchTerm)) {
      return true;
    }

    return false;
  });
});

// Group variations by date ranges
const groupedVariations: ComputedRef<
  Array<{
    dateRange: string;
    isActive: boolean;
    variations: ClientServiceRateVariationsWithSelection[];
  }>
> = computed(() => {
  const groups = new Map<string, ClientServiceRateVariationsWithSelection[]>();
  const now = moment().valueOf();

  filteredRateVariations.value.forEach((variation) => {
    const fromDate =
      variation.validFromDate && variation.validFromDate > 0
        ? moment(variation.validFromDate).format('MMM DD, YYYY')
        : 'No start date';
    const toDate =
      variation.validToDate && variation.validToDate > 0
        ? moment(variation.validToDate).format('MMM DD, YYYY')
        : 'No end date';
    const dateRange = `${fromDate} - ${toDate}`;

    if (!groups.has(dateRange)) {
      groups.set(dateRange, []);
    }
    groups.get(dateRange)!.push(variation);
  });

  return Array.from(groups.entries())
    .map(([dateRange, variations]) => {
      // Check if this date range is currently active
      const isActive = variations.some((variation) => {
        const validFrom = variation.validFromDate || 0;
        const validTo = variation.validToDate || Number.MAX_SAFE_INTEGER;
        return now >= validFrom && now <= validTo;
      });

      return {
        dateRange,
        isActive,
        variations: variations.sort((a, b) => {
          // Sort by service type, then rate type
          const serviceCompare =
            (a.serviceTypeId || 0) - (b.serviceTypeId || 0);
          if (serviceCompare !== 0) {
            return serviceCompare;
          }
          return (a.rateTypeId || 0) - (b.rateTypeId || 0);
        }),
      };
    })
    .sort((a, b) => {
      // Sort groups by date range, active first
      if (a.isActive && !b.isActive) {
        return -1;
      }
      if (!a.isActive && b.isActive) {
        return 1;
      }
      return a.dateRange.localeCompare(b.dateRange);
    });
});

// function toggleGroupSelection(
//   variations: ClientServiceRateVariationsWithSelection[],
//   isSelected: boolean,
// ): void {
//   // Set isSelected property on all variations in the group
//   variations.forEach((variation) => {
//     variation.isSelected = isSelected;
//   });
//   // Update the selectedVariations array
//   updateSelectionState();
// }

// function updateSelectionState(): void {
//   // Update the selectedVariations array based on isSelected properties
//   selectedVariations.value = rateVariations.value
//     .filter((variation) => variation.isSelected)
//     .map((variation) => variation._id || '')
//     .filter((id) => id !== '');
// }

function getServiceTypeName(serviceTypeId: number | null): string {
  if (!serviceTypeId) {
    return 'All Services';
  }
  return returnServiceTypeShortNameFromId(serviceTypeId, 'Unknown Service');
}

function getRateTypeName(rateTypeId: JobRateType | null): string {
  if (!rateTypeId) {
    return 'All Rate Types';
  }
  return returnRateTypeLongNameFromId(rateTypeId, 'Unknown Rate Type');
}

// Consolidated client name function with display options
function getClientNames(clientIds: string[], showAll: boolean = false): string {
  if (!clientIds || clientIds.length === 0) {
    return 'No clients';
  }

  const clientNames = clientIds.map((clientId) => {
    // Convert to string in case it's a number
    const idStr = String(clientId);
    const client = availableClients.value.find((c) => c.clientId === idStr);
    return client
      ? client.clientDisplayName || client.clientName
      : `Client ${idStr}`;
  });

  if (showAll) {
    // Return all client names joined by comma
    return clientNames.join(', ');
  } else {
    // Show only first client name with count for additional clients
    if (clientNames.length === 1) {
      return clientNames[0];
    } else {
      const additionalCount = clientNames.length - 1;
      return `${clientNames[0]} (+${additionalCount})`;
    }
  }
}

function formatPercentage(value: number | null): string {
  if (value === null || value === undefined) {
    return '-';
  }
  const sign = value >= 0 ? '+' : '';
  return `${sign}${value.toFixed(2)}%`;
}

function getPercentageClass(value: number | null): string {
  if (value === null || value === undefined) {
    return '';
  }
  if (value > 0) {
    return 'success--text';
  }
  if (value < 0) {
    return 'error--text';
  }
  return '';
}

function canEditVariation(variation: ClientServiceRateVariations): boolean {
  // If no clientId prop, allow editing (division level)
  if (!props.clientId) {
    return true;
  }

  // If clientId prop exists, only allow editing if variation applies to single client
  return variation.applyToIds.length <= 1;
}

function navigateToDivisionRateVariations(): void {
  // Navigate to the administration module and set UI context
  router.push('/administration').then(() => {
    useAppNavigationStore().setCurrentComponentId('#service-rate');
  });
}

async function removeClientFromVariation(
  variation: ClientServiceRateVariations,
): Promise<void> {
  if (!props.clientId || !variation._id) {
    return;
  }

  isSaving.value = true;
  try {
    // Create updated variation with client removed
    const updatedVariation = { ...variation };
    updatedVariation.applyToIds = variation.applyToIds.filter(function (id) {
      return id !== props.clientId;
    });

    // Save the updated variation
    const result =
      await serviceRateVariationsStore.saveClientServiceRateVariations(
        updatedVariation,
      );

    if (result) {
      showNotification('Client removed from rate variation successfully', {
        title: 'Service Rate Variation',
        type: HealthLevel.SUCCESS,
      });
      allDivisionVariations.value = [];
      await loadRateVariations();
    }
  } catch (error) {
    console.error('Error removing client from variation:', error);
    showNotification('Failed to remove client from rate variation', {
      title: 'Service Rate Variation',
      type: HealthLevel.ERROR,
    });
  } finally {
    isSaving.value = false;
  }
}

// Add client to existing variations functionality
async function addClientToVariation(
  variation: ClientServiceRateVariations,
): Promise<boolean> {
  if (!props.clientId || !variation._id) {
    return false;
  }

  try {
    // Create updated variation with client added
    const updatedVariation = { ...variation };

    // Check if client is already in the list to prevent duplicates
    if (!updatedVariation.applyToIds.includes(props.clientId)) {
      updatedVariation.applyToIds = [
        ...updatedVariation.applyToIds,
        props.clientId,
      ];
    }

    // Save the updated variation
    const result =
      await serviceRateVariationsStore.saveClientServiceRateVariations(
        updatedVariation,
      );

    return !!result;
  } catch (error) {
    console.error('Error adding client to variation:', error);
    return false;
  }
}

// Dialog management functions
function openRateVariationDialog(): void {
  // Pre-populate with client ID if in client details
  if (props.clientId) {
    selectedClientIds.value = [props.clientId];
    // Set default tab based on context
    activeTab.value = 0; // Default to "Create New" tab
  }
  showAddForm.value = true;
}

function getDialogTitle(): string {
  if (isEditing.value) {
    return 'Edit Service Rate Variation';
  }
  if (props.clientId) {
    return 'Manage Service Rate Variations';
  }
  return 'Add New Service Rate Variation';
}

async function loadExistingVariations(): Promise<void> {
  if (!props.clientId) {
    return;
  }

  isLoading.value = true;
  try {
    let allVariations: ClientServiceRateVariations[] | null = null;

    // Check if we have cached all division variations and can reuse them
    if (
      allDivisionVariations.value.length > 0 &&
      selectedStatus.value === ClientServiceRateVariationsStatus.ALL
    ) {
      // Use cached data - no API call needed!
      allVariations = allDivisionVariations.value;
    } else {
      // Need to load all variations since we don't have complete cached data
      allVariations =
        await serviceRateVariationsStore.getDivisionRateVariationsByStatus(
          ClientServiceRateVariationsStatus.ALL,
        );

      // Update cache for future use
      if (allVariations) {
        allDivisionVariations.value = allVariations;
      }
    }

    if (allVariations) {
      // Filter out variations that already include this client
      const availableVariations = allVariations.filter((variation) => {
        return !variation.applyToIds.includes(props.clientId!);
      });

      // Add selection properties to each variation
      existingVariationsForSelection.value =
        addSelectionProperties(availableVariations);
    } else {
      existingVariationsForSelection.value = [];
    }
  } catch (error) {
    showNotification('Failed to load existing variations');
    existingVariationsForSelection.value = [];
  } finally {
    isLoading.value = false;
  }
}

async function addClientToSelectedVariations(): Promise<void> {
  if (!props.clientId || selectedExistingVariations.value.length === 0) {
    return;
  }

  isSaving.value = true;
  let successCount = 0;
  let errorCount = 0;

  try {
    for (const variationId of selectedExistingVariations.value) {
      const variation = existingVariationsForSelection.value.find(
        (v) => v._id === variationId,
      );
      if (variation) {
        const success = await addClientToVariation(variation);
        if (success) {
          successCount++;
        } else {
          errorCount++;
        }
      }
    }

    if (successCount > 0) {
      showNotification(
        `Successfully added client to ${successCount} rate variation(s)${
          errorCount > 0 ? `. ${errorCount} failed.` : ''
        }`,
        {
          title: 'Service Rate Variation',
          type: HealthLevel.SUCCESS,
        },
      );
      allDivisionVariations.value = [];
      await loadRateVariations();
      cancelForm();
    }
  } catch (error) {
    console.error('Error adding client to selected variations:', error);
    showNotification('Failed to add client to rate variations', {
      title: 'Service Rate Variation',
      type: HealthLevel.ERROR,
    });
  } finally {
    isSaving.value = false;
  }
}

function formatDateRange(variation: ClientServiceRateVariations): string {
  const fromDate = returnFormattedDate(variation.validFromDate || 0);
  const toDate = returnFormattedDate(variation.validToDate || 0);

  if (fromDate === '-' && toDate === '-') {
    return 'No dates set';
  } else if (fromDate === '-') {
    return `Until ${toDate}`;
  } else if (toDate === '-') {
    return `From ${fromDate}`;
  } else {
    return `${fromDate} - ${toDate}`;
  }
}

function getStatusText(variation: ClientServiceRateVariations): string {
  const now = Date.now();
  const validFrom = variation.validFromDate || 0;
  const validTo = variation.validToDate || Number.MAX_SAFE_INTEGER;

  if (now < validFrom) {
    return 'Future';
  } else if (now >= validFrom && now <= validTo) {
    return 'Active';
  } else {
    return 'Expired';
  }
}

function resetForm(): void {
  newVariation.value = new ClientServiceRateVariations();
  selectedClientIds.value = [];
  applyToAllServiceTypes.value = false;
  applyToAllRateTypes.value = false;
}

function cancelForm(): void {
  resetForm();
  showAddForm.value = false;
  isEditing.value = false;
  editingVariation.value = null;
  activeTab.value = 0;
  // Clear existing variations selection
  existingVariationsForSelection.value = [];
  selectedExistingVariations.value = [];
}

// Apply to all checkbox handlers
function onApplyToAllServiceTypesChange(): void {
  if (applyToAllServiceTypes.value) {
    newVariation.value.serviceTypeId = null;
  }
}

function onApplyToAllRateTypesChange(): void {
  if (applyToAllRateTypes.value) {
    newVariation.value.rateTypeId = null;
  }
}

function setValidFromDate(epochTime: number): void {
  newVariation.value.validFromDate = epochTime;
}

function setValidToDate(epochTime: number): void {
  newVariation.value.validToDate = epochTime;
}

function setBulkValidFromDate(epochTime: number): void {
  bulkEdit.value.validFromDate = epochTime;
}

function setBulkValidToDate(epochTime: number): void {
  bulkEdit.value.validToDate = epochTime;
}

// Helper function to add selection properties to variations
function addSelectionProperties(
  variations: ClientServiceRateVariations[],
): ClientServiceRateVariationsWithSelection[] {
  return variations.map((variation) => ({
    ...variation,
    isSelected: false,
  }));
}

// Data loading
async function loadRateVariations(): Promise<void> {
  isLoading.value = true;
  try {
    // Always load all division variations by status
    const allVariations =
      await serviceRateVariationsStore.getDivisionRateVariationsByStatus(
        selectedStatus.value,
      );

    if (allVariations) {
      // Cache all variations for reuse in "Add to Existing" functionality
      // Only cache if we loaded ALL status to have complete data
      if (selectedStatus.value === ClientServiceRateVariationsStatus.ALL) {
        allDivisionVariations.value = allVariations;
      }

      let filteredVariations = allVariations;

      // Filter by client ID if props.clientId is provided
      if (props.clientId) {
        filteredVariations = allVariations.filter((variation) => {
          return variation.applyToIds.includes(props.clientId!);
        });
      }

      // Add selection properties to each variation
      rateVariations.value = addSelectionProperties(filteredVariations);
    } else {
      rateVariations.value = [];
    }
  } catch (error) {
    console.error('Error loading rate variations:', error);
    showNotification('Failed to load rate variations');
  } finally {
    isLoading.value = false;
  }
}

// Smart suggestions
function checkForExistingSimilarVariations(): boolean {
  // Skip smart suggestions when editing existing variations
  if (isEditing.value) {
    return false;
  }

  if (
    !newVariation.value.clientAdjustmentPercentage &&
    !newVariation.value.fleetAssetAdjustmentPercentage
  ) {
    return false;
  }

  // Use existing loaded variations data (filtered by client if applicable)
  const divisionVariations = rateVariations.value;

  if (!divisionVariations || divisionVariations.length === 0) {
    return false;
  }

  // Look for EXACT duplicates only (service type + rate type + both percentages match)
  const exactDuplicates = divisionVariations.filter((variation) => {
    const sameClientPercentage =
      variation.clientAdjustmentPercentage ===
      newVariation.value.clientAdjustmentPercentage;
    const sameFleetPercentage =
      variation.fleetAssetAdjustmentPercentage ===
      newVariation.value.fleetAssetAdjustmentPercentage;
    const sameScope =
      variation.serviceTypeId === newVariation.value.serviceTypeId &&
      variation.rateTypeId === newVariation.value.rateTypeId;

    // Only return true for EXACT duplicates (all criteria must match)
    const isExactDuplicate =
      sameClientPercentage && sameFleetPercentage && sameScope;

    return isExactDuplicate;
  });

  if (exactDuplicates.length > 0) {
    const variation = exactDuplicates[0];

    // Check if date ranges are the same
    const sameDateRange =
      variation.validFromDate === newVariation.value.validFromDate &&
      variation.validToDate === newVariation.value.validToDate;

    if (sameDateRange) {
      // Same date range - directly open for editing instead of showing dialog
      // Show notification to user
      showNotification(
        'Opening existing variation with identical settings for editing',
        {
          title: 'Service Rate Variation',
          type: HealthLevel.WARNING,
        },
      );

      // Directly open the existing variation for editing
      handleEditExistingVariation(variation);

      return true; // Suggestion handled by direct edit
    } else {
      // Different date ranges - show suggestion dialog

      suggestionDialogExistingVariation.value = variation;
      suggestionDialogPrimaryAction.value = () =>
        handleEditExistingVariation(variation);
      showSuggestionDialog.value = true;
    }

    return true; // Suggestion found and shown
  }

  return false;
}

function handleEditExistingVariation(
  existingVariation: ClientServiceRateVariations,
): void {
  // Close the current form dialog
  showAddForm.value = false;

  // Close the suggestion dialog
  showSuggestionDialog.value = false;

  // Capture the clients that were selected in the new variation form
  // Extract client IDs from selectedClientIds (handle both string IDs and objects)
  const newlySelectedClientIds = selectedClientIds.value.map((item) => {
    if (typeof item === 'string') {
      return item;
    } else if (typeof item === 'object' && item.clientId) {
      return item.clientId;
    }
    return String(item);
  });

  // Open the existing variation in edit mode
  setTimeout(() => {
    editVariation(existingVariation);

    // After the edit dialog opens, merge the newly selected clients
    setTimeout(() => {
      // Get existing client IDs from the variation
      const existingClientIds = existingVariation.applyToIds || [];

      // Combine existing clients with newly selected clients (avoid duplicates)
      const mergedClientIds = [
        ...new Set([...existingClientIds, ...newlySelectedClientIds]),
      ];

      // Update the selected clients in the edit form
      // Map IDs back to full client objects for proper display
      selectedClientIds.value = mergedClientIds.map((id) => {
        const idStr = String(id);
        const client = availableClients.value.find((c) => c.clientId === idStr);
        return client || idStr; // Return client object if found, otherwise fallback to ID string
      });

      // Show notification to user about the merge
      if (newlySelectedClientIds.length > 0) {
        const addedCount = mergedClientIds.length - existingClientIds.length;
        if (addedCount > 0) {
          showNotification(
            `Added ${addedCount} client(s) from your new variation to the existing variation`,
            {
              title: 'Service Rate Variation',
              type: HealthLevel.WARNING,
            },
          );
        } else {
          showNotification(
            'Selected clients were already included in the existing variation',
            {
              title: 'Service Rate Variation',
              type: HealthLevel.WARNING,
            },
          );
        }
      }
    }, 200); // Additional delay to ensure edit form is fully loaded
  }, 100); // Small delay to ensure dialog transitions work smoothly
}

async function saveRateVariation(): Promise<void> {
  isSaving.value = true;
  try {
    // Check for smart suggestions before saving
    const suggestionShown = checkForExistingSimilarVariations();

    if (suggestionShown) {
      isSaving.value = false;
      return; // Stop the save process to let user respond to suggestion
    }

    // Create the rate variation object
    const variation = new ClientServiceRateVariations();
    variation.rateOwnerId = '0'; // Division-level entry
    if (props.clientId) {
      variation.applyToIds = [props.clientId];
    } else {
      // Validate that clients are selected
      if (!selectedClientIds.value || selectedClientIds.value.length === 0) {
        showNotification('Please select at least one client');
        isSaving.value = false;
        return;
      }

      // Extract only client IDs (handle both string IDs and objects)
      const clientIds = selectedClientIds.value.map((item) => {
        if (typeof item === 'string') {
          return item;
        } else if (typeof item === 'object' && item.clientId) {
          return item.clientId;
        }
        return String(item);
      });
      variation.applyToIds = clientIds; // Apply to selected clients (only IDs)
    }

    // Set service type and rate type (null if "apply to all" is checked)
    variation.serviceTypeId = applyToAllServiceTypes.value
      ? null
      : newVariation.value.serviceTypeId || null;
    variation.rateTypeId = applyToAllRateTypes.value
      ? null
      : newVariation.value.rateTypeId || null;

    variation.clientAdjustmentPercentage =
      newVariation.value.clientAdjustmentPercentage;
    variation.fleetAssetAdjustmentPercentage =
      newVariation.value.fleetAssetAdjustmentPercentage;
    // Ensure dates are numbers, use 0 if not set
    variation.validFromDate = newVariation.value.validFromDate || 0;
    variation.validToDate = newVariation.value.validToDate || 0;

    let result: ClientServiceRateVariations | null;

    if (isEditing.value && editingVariation.value) {
      // Update existing variation
      variation._id = editingVariation.value._id;
      result =
        await serviceRateVariationsStore.saveClientServiceRateVariations(
          variation,
        );
    } else {
      // Create new variation
      result =
        await serviceRateVariationsStore.saveClientServiceRateVariations(
          variation,
        );
    }

    if (result) {
      showNotification('Rate variation saved successfully.', {
        title: 'Service Rate Variation',
        type: HealthLevel.SUCCESS,
      });
      allDivisionVariations.value = [];
      await loadRateVariations();
      cancelForm();
    }
  } catch (error) {
    console.error('Error saving rate variation:', error);
    showNotification('Failed to save rate variation', {
      title: 'Service Rate Variation',
      type: HealthLevel.ERROR,
    });
  } finally {
    isSaving.value = false;
  }
}

function editVariation(variation: ClientServiceRateVariations): void {
  editingVariation.value = variation;
  isEditing.value = true;
  showAddForm.value = true;

  // Copy all properties from the variation
  Object.assign(newVariation.value, variation);

  // Set the selected clients (map IDs back to full client objects for proper display)
  selectedClientIds.value = variation.applyToIds.map((id) => {
    const idStr = String(id);
    const client = availableClients.value.find((c) => c.clientId === idStr);
    return client || idStr; // Return client object if found, otherwise fallback to ID string
  });

  // Set apply to all checkboxes based on null values
  applyToAllServiceTypes.value = variation.serviceTypeId === null;
  applyToAllRateTypes.value = variation.rateTypeId === null;
}

// Bulk operations
async function applyBulkEdit(): Promise<void> {
  if (selectedVariations.value.length === 0) {
    return;
  }

  isSaving.value = true;
  try {
    const variationsToUpdate: ClientServiceRateVariations[] = [];

    for (const variationId of selectedVariations.value) {
      const variation = rateVariations.value.find((v) => v._id === variationId);
      if (!variation) {
        continue;
      }

      const updatedVariation = { ...variation };

      // Apply bulk changes only if values are provided
      if (
        bulkEdit.value.clientAdjustmentPercentage !== null &&
        bulkEdit.value.clientAdjustmentPercentage !== undefined
      ) {
        updatedVariation.clientAdjustmentPercentage =
          bulkEdit.value.clientAdjustmentPercentage;
      }
      if (
        bulkEdit.value.fleetAssetAdjustmentPercentage !== null &&
        bulkEdit.value.fleetAssetAdjustmentPercentage !== undefined
      ) {
        updatedVariation.fleetAssetAdjustmentPercentage =
          bulkEdit.value.fleetAssetAdjustmentPercentage;
      }
      // Ensure dates are numbers, use existing value if bulk edit value is 0 or not set
      if (bulkEdit.value.validFromDate && bulkEdit.value.validFromDate > 0) {
        updatedVariation.validFromDate = bulkEdit.value.validFromDate;
      }
      if (bulkEdit.value.validToDate && bulkEdit.value.validToDate > 0) {
        updatedVariation.validToDate = bulkEdit.value.validToDate;
      }

      variationsToUpdate.push(updatedVariation);
    }

    if (variationsToUpdate.length > 0) {
      const result =
        await serviceRateVariationsStore.saveAllClientServiceRateVariations(
          variationsToUpdate,
        );
      if (result) {
        showNotification(
          `Successfully updated ${variationsToUpdate.length} rate variations`,
          {
            title: 'Service Rate Variation',
            type: HealthLevel.SUCCESS,
          },
        );
        allDivisionVariations.value = [];
        await loadRateVariations();
        // Clear isSelected properties on all variations
        rateVariations.value.forEach((variation) => {
          variation.isSelected = false;
        });
        // Clear the selectedVariations array
        selectedVariations.value = [];
        showBulkEditDialog.value = false;

        // Reset bulk edit form
        bulkEdit.value = {
          clientAdjustmentPercentage: null,
          fleetAssetAdjustmentPercentage: null,
          validFromDate: 0,
          validToDate: 0,
        };
      }
    }
  } catch (error) {
    console.error('Error applying bulk edit:', error);
    showNotification('Failed to update rate variations', {
      title: 'Service Rate Variation',
      type: HealthLevel.ERROR,
    });
  } finally {
    isSaving.value = false;
  }
}

// Watch for changes in individual selections and update the selectedVariations array
watch(
  () => rateVariations.value.map((v) => v.isSelected),
  () => {
    updateSelectionState();
  },
);

// Watch for changes in existing variations selection
watch(
  () => existingVariationsForSelection.value.map((v) => v.isSelected),
  () => {
    selectedExistingVariations.value = existingVariationsForSelection.value
      .filter((variation) => variation.isSelected)
      .map((variation) => variation._id || '')
      .filter((id) => id !== '');
  },
);

// Watch for tab changes and load existing variations when needed
watch(
  () => activeTab.value,
  (newTab) => {
    if (newTab === 1 && props.clientId && showAddForm.value) {
      // Tab 1 is "Add to Existing" and we're in client context
      loadExistingVariations();
    }
  },
);

// Watch for dialog open and load existing variations if on the right tab
watch(
  () => showAddForm.value,
  (isOpen) => {
    if (isOpen && activeTab.value === 1 && props.clientId) {
      loadExistingVariations();
    }
  },
);

// Watch for changes in status filter and reload variations
watch(
  () => selectedStatus.value,
  () => {
    allDivisionVariations.value = [];
    loadRateVariations();
  },
);

// Watch for clientId prop changes and preselect client
watch(
  () => props.clientId,
  (newClientId) => {
    if (newClientId) {
      // Preselect the client in the form
      selectedClientIds.value = [newClientId];
    }
    // Reload variations when clientId changes
    loadRateVariations();
  },
);

onMounted(() => {
  loadRateVariations(); // Load all variations
});
</script>

<style scoped lang="scss">
.table-group-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-left: 12px;
  padding-bottom: 8px;
}

/* Service Rate Variations Table Styling */
.service-rate-variations-table {
  position: relative;

  .table-header-left-action-icon-container {
    position: absolute;
    top: 4px;
    left: 10px;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: var(--background-color-300);
  }
}

.checkbox-type-cell {
  text-align: center;
  max-width: 80px !important;
  width: 80px !important;
}
.client-type-cell {
  max-width: 240px !important;
  width: 240px !important;
}

.status-cell {
  &.Active {
    color: $success;
  }
  &.Future {
    color: $info;
  }
  &.Expired {
    color: $error;
  }
  color: var(--light-text-color);
}

.multi-client-message {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 4px;

  .division-link {
    color: $accent;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 700;
  }
}

.tab-content-container {
  height: calc(80vh - 160px);
  min-height: calc(80vh - 160px);
  max-height: calc(80vh - 160px);
  overflow-y: auto;
}
</style>

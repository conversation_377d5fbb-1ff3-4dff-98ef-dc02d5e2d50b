<template>
  <v-layout>
    <v-flex
      class="job-booking-form-container"
      ref="jobBookingFormContainer"
      id="form"
      :class="sessionManager.isClientPortal() ? 'md12' : 'md9'"
    >
      <v-layout
        class="new-booking-dialog"
        row
        wrap
        v-if="keyDetailsDialogIsOpen && viewType !== JobOperationType.EDIT"
      >
        <v-flex md12 pt-2>
          <div class="d-flex justify-space-between align-center">
            <div>
              <h1>Book a New Job</h1>
              <p>Please enter details for the job</p>
            </div>
            <JobBookingSearchQuoteDialog
              v-if="keyDetailsDialogIsOpen && !sessionManager.isClientPortal()"
              @applyQuoteId="applyQuoteFromSearch"
            ></JobBookingSearchQuoteDialog>
          </div>
        </v-flex>

        <v-flex md12>
          <v-layout>
            <JobBookingKeyDetailsDialog
              ref="jobBookingKeyDetailsDialog"
              :key="jobDetails.client.id + '-' + viewType + updateCounter"
              :jobDetails="jobDetails"
              :clientId="clientId"
              :clientDetails="clientDetails"
              :isNewJob="true"
              :isDialogOpen="true"
              :canEditForm="true"
              @applyChanges="copyKeyDetailsToJob"
              @clientDetailsSet="updateClientDetails"
              @cancelChanges="cancelChanges"
            ></JobBookingKeyDetailsDialog>
          </v-layout>
        </v-flex>
      </v-layout>
      <v-layout class="form-container">
        <JobBookingForm
          v-show="!keyDetailsDialogIsOpen || viewType === JobOperationType.EDIT"
          ref="jobBookingForm"
          :key="jobDetails.jobId + '-' + viewType + updateCounter"
          :jobDetails="jobDetails"
          :clientId="clientId"
          :clientDetails="
            sessionManager.isClientPortal() ? clientDetails : null
          "
          :isDialogOpen="keyDetailsDialogIsOpen"
          :scrollY="scrollY"
          :options="formOptions"
          @finishedEditing="setNewJobBooking"
          @clearOptions="formOptions = {}"
        ></JobBookingForm>
      </v-layout>
      <JobBookingSuccessDialog
        v-if="successDialogIsOpen && successDialogConfig"
        class="success-dialog"
        :isDialogOpen="successDialogIsOpen"
        :bookingDetails="successDialogConfig"
        @returnToDashboard="returnToDashboard"
        @bookNewJob="closeSuccessDialog"
      ></JobBookingSuccessDialog>
    </v-flex>
    <v-flex md3 v-if="!sessionManager.isClientPortal()">
      <JobBookingSideBar
        :clientDetails="clientDetails"
        :jobDetails="jobDetails"
      />
    </v-flex>
  </v-layout>
</template>

<script setup lang="ts">
import JobBookingForm, {
  JobBookingFormOptions,
} from '@/components/booking/job_booking_form.vue';
import JobBookingKeyDetailsDialog, {
  JobKeyDetailsUpdate,
} from '@/components/booking/key_details/job_booking_key_details_dialog.vue';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { JobSuccessDialogConfig } from '@/interface-models/Jobs/JobBookingType';
import { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import { JobBookingPageConfig } from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceDialogConfig';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import { useMittListener } from '@/utils/useMittListener';
import {
  computed,
  ComputedRef,
  nextTick,
  onMounted,
  onUnmounted,
  Ref,
  ref,
} from 'vue';
import JobBookingSearchQuoteDialog from './job_booking_search_quote_dialog.vue';
import JobBookingSideBar from './job_booking_side_bar.vue';
import JobBookingSuccessDialog from './job_booking_success_dialog.vue';

// In ClientPortal, clientId and clientDetails will be passed as props and the
// Client selection will be disabled.
const props = withDefaults(
  defineProps<{
    clientId?: string | null;
    clientDetails?: ClientDetails | null;
  }>(),
  {
    clientId: null,
    clientDetails: null,
  },
);

const operationsStore = useOperationsStore();

const viewType: Ref<JobOperationType.NEW | JobOperationType.EDIT> = ref(
  JobOperationType.NEW,
);
const updateCounter: Ref<number> = ref(0);
const scrollY = ref(0);
const jobBookingFormContainer = ref<HTMLElement | null>(null);
const jobDetails: Ref<JobDetails> = ref(new JobDetails());
let jobDetailsClone: JobDetails = new JobDetails();
const keyDetailsDialogIsOpen: Ref<boolean> = ref(true);

// Add reactive clientDetails ref to track selected client details
const clientDetails: Ref<ClientDetails | null> = ref(props.clientDetails);

const jobBookingForm: Ref<any> = ref(null);
const jobBookingKeyDetailsDialog: Ref<any> = ref(null);
const formOptions: Ref<JobBookingFormOptions> = ref({});

const successDialogIsOpen: Ref<boolean> = ref(false);
const successDialogConfig: Ref<JobSuccessDialogConfig | null> = ref(null);

const emit = defineEmits<{
  (event: 'selectMenuItem', payload: number): void;
}>();
/**
 * Confirms whether to discard changes or not.
 *
 * @returns {boolean} - Returns true if changes can be discarded, otherwise false.
 */
function confirmDiscardChanges(): boolean {
  const hasChanges = checkForJobChanges();
  if (hasChanges) {
    return window.confirm(
      'You have unsaved changes. Are you sure you want to discard them?',
    );
  } else {
    return true;
  }
}

/**
 * Calls method in JobBookingForm to copy key details to job. This is called on
 * the 'applyChanges' emit from the JobBookingKeyDetailsDialog when booking a
 * new job.
 * @param details - The client specific details required to book a job (service
 * rate, fuel surcharge, unassigned puds etc.)
 */
function copyKeyDetailsToJob(details: JobKeyDetailsUpdate) {
  keyDetailsDialogIsOpen.value = false;
  // Update the clientDetails ref so that NotesSidePanel can access client notes
  clientDetails.value = details.clientDetails;
  if (jobBookingForm.value) {
    jobBookingForm.value!.copyKeyDetailsToJob(details);
  }
}

/**
 * Calls method in jobBookingKeyDetailsDialog to apply quote details and set client details for job. This is called on
 * the 'applyChanges' emit from the QuoteSearchDialog when booking a Search Quote.
 * @param QuoteDetails - The Quote specific details required to set client details and book job
 */
function applyQuoteFromSearch(quote) {
  if (quote.quoteDetails) {
    jobBookingKeyDetailsDialog.value.applyPropertiesFromQuoteSearch(quote);
  }
}

/**
 * Updates the clientDetails ref immediately when a client is selected in the JobBookingKeyDetailsDialog.
 * This allows the NotesSidePanel to display client notes instantly.
 * @param client - The ClientDetails object that was set, or null if cleared
 */
function updateClientDetails(client: ClientDetails | null) {
  clientDetails.value = client;
}

function cancelChanges(value: boolean) {
  keyDetailsDialogIsOpen.value = value;
}

const isAuthorisedAdmin: ComputedRef<boolean> = computed(() => {
  return hasAdminRole();
});

/**
 * Checks for changes between the current jobDetails and the clone of the
 * jobDetails. Returns true if changes are detected, otherwise false.
 */
function checkForJobChanges() {
  const changes = compareJobObjects(jobDetails.value, jobDetailsClone);
  if (changes.length > 0) {
    if (isAuthorisedAdmin.value) {
      console.log('Changes detected:');
      console.table(changes);
      changes.forEach((change) => {
        console.log(`Property: ${change.property}`);
        console.log('Old Value:', change.oldValue);
        console.log('New Value:', change.newValue);
      });
    }
    return true;
  } else {
    console.log('No changes detected.');
    return false;
  }
}

/**
 * Compares two JobDetails objects and returns an array of changes between the
 * two objects.
 * NOTE: Currently not working properly - incorrectly detecting changes in additionalData
 */
function compareJobObjects(
  newObj: JobDetails,
  oldObj: JobDetails,
): {
  property: string;
  oldValue: any;
  newValue: any;
}[] {
  const changes: {
    property: string;
    oldValue: any;
    newValue: any;
  }[] = [];
  for (const key in newObj) {
    // Don't check additionalJobData as it's not relevant to the booking
    if (key === 'additionalJobData') {
      continue;
    }
    if (newObj.hasOwnProperty(key)) {
      // If looking at the accounting object, check only specific fields as the
      // totals is recalculated on the form automatically (and it's not possible
      // to affect a change in the totals without changing the specific fields)
      if (key === 'accounting') {
        const accountingFields = [
          'clientRates',
          'fleetAssetRates',
          'additionalCharges',
        ];
        for (const field of accountingFields) {
          if (
            JSON.stringify(newObj[key][field]) !==
            JSON.stringify(oldObj[key][field])
          ) {
            changes.push({
              property: `${key}.${field}`,
              oldValue: oldObj[key][field],
              newValue: newObj[key][field],
            });
          }
        }
      } else if (JSON.stringify(newObj[key]) !== JSON.stringify(oldObj[key])) {
        changes.push({
          property: key,
          oldValue: oldObj[key],
          newValue: newObj[key],
        });
      }
    }
  }
  return changes;
}

/**
 * Set the view type to NEW and resets the component state to a fresh JobDetails
 * object
 */
function setNewJobBooking(payload: {
  confirmDiscard: boolean;
  clientId?: string;
  unassignedPudIds?: string[];
  jumpToDashboard?: boolean;
  successDialogConfig?: JobSuccessDialogConfig;
}) {
  const { confirmDiscard, clientId, unassignedPudIds, jumpToDashboard } =
    payload;
  // Confirm the discard of changes before proceeding if confirmDiscard is true
  if (confirmDiscard && !confirmDiscardChanges()) {
    return;
  }
  viewType.value = JobOperationType.NEW;
  setJobDetails(new JobDetails());

  // Reset clientDetails when starting a new job
  clientDetails.value = props.clientDetails;

  // If any unassigned pud config was provided, set those details to the job
  if (clientId && unassignedPudIds?.length) {
    console.log(
      'Setting client and unassigned puds',
      clientId,
      unassignedPudIds,
    );
    // Set to jobDetails before mounting keyDetailsDialog
    jobDetails.value.client.id = clientId;
  }

  keyDetailsDialogIsOpen.value = true;
  updateCounter.value++;
  if (payload.successDialogConfig) {
    successDialogConfig.value = payload.successDialogConfig;
    successDialogIsOpen.value = true;
    return;
  } else if (jumpToDashboard) {
    returnToDashboard();
    return;
  }

  // If we set the clientId to the job, then we should manually call method in
  // child component to set the client details, and fetch client-related booking
  // data
  if (clientId && unassignedPudIds?.length) {
    nextTick(() => {
      jobBookingKeyDetailsDialog.value?.getAndSetClientDetails(
        clientId,
        unassignedPudIds,
      );
    });
  }
}

/**
 * Resets variables and closes the success dialog.
 */
function closeSuccessDialog() {
  successDialogIsOpen.value = false;
  successDialogConfig.value = null;
}

/**
 * Closes success dialog if it's open, and navigates to the dashboard on both
 * client and operations portals.
 */
function returnToDashboard() {
  closeSuccessDialog();
  // If jumpToDashboard is true, navigate to the operations dashboard
  if (sessionManager.isClientPortal()) {
    emit('selectMenuItem', 0);
  } else {
    useAppNavigationStore().setCurrentComponentId('#dashboard-main');
  }
}

/**
 * Sets the component state to edit mode for the provided jobId. Requests the
 * full JobDetails object and sets to the working copy.
 * @param jobId
 */
async function editExistingJob(
  jobId: number,
  clientId?: string,
  unassignedPudIds?: string[],
  reBookJobDetails?: JobDetails,
) {
  // If the jobId is the same as the currentJobDetails, do nothing as we don't
  // want to overwrite anything.
  if (!!jobDetails.value.jobId && jobId === jobDetails.value.jobId) {
    return;
  }
  // If the jobId is different from the current jobDetails, confirm the discard
  // of changes. If the user cancels, return (do nothing)
  if (jobId !== jobDetails.value.jobId && !confirmDiscardChanges()) {
    return;
  }

  // If the config contains a jobId, request the job details and set the view
  // type to EDIT.
  const job = reBookJobDetails
    ? reBookJobDetails
    : await operationsStore.requestJobDetailsByJobId(jobId!);
  if (!job) {
    setNewJobBooking({ confirmDiscard: false });
    return;
  }
  // If we set the clientId to the job, then we should manually call method in
  // child component to set the client details, and fetch client-related booking
  // data
  if (clientId && unassignedPudIds?.length) {
    formOptions.value = {
      unassignedPudIds,
    };
  }
  viewType.value = JobOperationType.EDIT;
  keyDetailsDialogIsOpen.value = false;
  await setJobDetails(job);
}

/**
 * Sets the jobDetails object to the provided job object and creates a deep copy
 * of the object to track changes. Called with a clean JobDetails object when
 * starting a new job, and with the full JobDetails object from api request when
 * editing an existing job.
 * @param job - The job object to set jobDetails to.
 */
async function setJobDetails(job: JobDetails) {
  jobDetails.value = job;
  jobDetailsClone = deepCopy(job);

  // Set clientDetails from job details when editing an existing job
  if (job.client && job.client.id) {
    try {
      // Fetch complete client details from API instead of creating a basic object
      const completeClientDetails =
        await useClientDetailsStore().requestClientDetailsByClientId(
          job.client.id,
        );
      if (completeClientDetails) {
        clientDetails.value = completeClientDetails;
      } else {
        // Fallback to basic client details if API call fails
        const jobClientDetails = new ClientDetails();
        jobClientDetails.clientId = job.client.id;
        jobClientDetails.clientName = job.client.clientName;
        jobClientDetails.tradingName = job.client.clientName;
        jobClientDetails.specialInstructions = job.notes || [];
        clientDetails.value = jobClientDetails;
      }
    } catch (error) {
      console.error('Error fetching client details:', error);
      // Fallback to basic client details if API call fails
      const jobClientDetails = new ClientDetails();
      jobClientDetails.clientId = job.client.id;
      jobClientDetails.clientName = job.client.clientName;
      jobClientDetails.tradingName = job.client.clientName;
      jobClientDetails.specialInstructions = job.notes || [];
      clientDetails.value = jobClientDetails;
    }
  }
}

/**
 * Handle the job configuration published by the event listener.
 */
async function handleJobConfig(config: JobBookingPageConfig | null) {
  // If the config is null, show a generic error message and reset the component
  if (!config) {
    showNotification(GENERIC_ERROR_MESSAGE);
    setNewJobBooking({ confirmDiscard: false });
    return;
  }
  // Set component state to either NEW or EDIT mode depending on the config
  const isNewBooking =
    config.operationType === JobOperationType.NEW ||
    !config.jobId ||
    config.jobId === -1;
  if (isNewBooking) {
    setNewJobBooking({
      confirmDiscard: true,
      clientId: config.clientId,
      unassignedPudIds: config.unassignedPudIds,
    });
  } else {
    await editExistingJob(
      config.jobId,
      config.clientId,
      config.unassignedPudIds,
      config.jobDetails,
    );
  }
}

useMittListener('bookJobWithConfig', handleJobConfig);

// find value of scrollY in form
const updateScrollY = () => {
  if (jobBookingFormContainer.value) {
    scrollY.value = jobBookingFormContainer.value.scrollTop;
  }
};
// add scroll event listener to ref jobBookingFormContainer
onMounted(() => {
  if (jobBookingFormContainer.value) {
    jobBookingFormContainer.value.addEventListener('scroll', updateScrollY);
  }

  if (operationsStore.reBookJobBookingPageConfig) {
    Mitt.emit('bookJobWithConfig', operationsStore.reBookJobBookingPageConfig);
    operationsStore.reBookJobBookingPageConfig = null;
  }
});

// remove scroll event listener from ref jobBookingFormContainer
onUnmounted(() => {
  if (jobBookingFormContainer.value) {
    jobBookingFormContainer.value.removeEventListener('scroll', updateScrollY);
  }
});
</script>
<style scoped lang="scss">
.light-theme {
  .job-booking-form-container {
    height: calc(100vh - 100px);
    overflow-y: auto;
  }
}
.job-booking-form-container {
  overflow-x: hidden;
  overflow-y: auto;
  height: calc(100vh - 100px);
  position: relative;
  display: flex;
  margin-top: 60px;
  padding-bottom: 60px;
  justify-content: center;
  align-items: flex-start;
  .form-container {
    margin-top: 6px;
  }
}

.new-booking-dialog {
  position: absolute;
  width: 86%;
  background-color: var(--background-color-400);
  box-shadow: var(--box-shadow);
  border-radius: $border-radius-lg;
  border: 1px solid $border-color;
  transform: translate(0, 14%);

  h1 {
    font-family: $sub-font-family;
    font-size: $font-size-24;
    color: var(--primary);
    padding-left: 30px;
    margin-top: 6px;
  }
  p {
    padding-left: 30px;
    font-size: $font-size-18;
    color: var(--light-text-color);
    margin-bottom: 6px;
  }
}
</style>

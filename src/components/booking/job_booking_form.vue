<template>
  <v-layout v-if="!loadingKeyData" ref="top" class="job-booking-form">
    <ContentDialog
      :showDialog.sync="noteDialogIsOpen"
      title="Add Note to Job"
      width="40%"
      contentPadding="pa-0"
      :showActions="false"
      :isConfirmUnsaved="false"
      :isDisabled="false"
      :isLoading="false"
      @cancel="noteDialogIsOpen = false"
      confirmBtnText="Confirm"
    >
      <v-layout>
        <v-flex md12 class="body-scrollable--75">
          <NotesEditor
            v-if="noteDialogIsOpen"
            :isBookingScreen="true"
            :communications="jobDetails.notes"
            :isEdited="true"
            :isAddingNote="noteDialogIsOpen"
            @setIsAddingNote="noteDialogIsOpen = $event"
            :type="3"
            :jobDetails="jobDetails"
            :isDispatchNote="isDispatchNote"
            :enableCommunicationTypeSelect="!sessionManager.isClientPortal()"
            :enableVisibilitySelect="!sessionManager.isClientPortal()"
            :isClientVisibilityOnly="sessionManager.isClientPortal()"
            :jobNoteLevel="JobNoteLevel.JOB"
          >
          </NotesEditor>
        </v-flex>
      </v-layout>
    </ContentDialog>
    <!-- Side Summary -->
    <JobBookingSideSummary
      :jobDetails="jobDetails"
      :recurrenceType="recurrenceType"
      :recurrenceDetails="recurrenceDetails"
      :jobDate="jobDate"
      @scrollTo="scrollTo"
      @scrollToTop="scrollToTop"
      @highlightElement="highlight = $event"
      @openPricingDialog="pricingDialogIsOpen = true"
    >
    </JobBookingSideSummary>
    <!-- Top Bar -->
    <v-flex offset-md2 md10>
      <JobBookingFormTopBar
        :jobDetails="jobDetails"
        :selectedClientDetails="selectedClientDetails"
        :awaitingSaveResponse="awaitingSaveResponse"
        :recurrenceType="recurrenceType"
        @cancel="cancel"
        @saveQuote="saveQuote"
        @confirmJobSave="saveAdhocOrPermanentJob"
      />
      <!-- KEY INFORMATION -->
      <v-layout class="form-section">
        <v-flex
          class="form-card"
          :class="{
            'client-portal': sessionManager.isClientPortal(),
            highlight: highlight === 'jobSummary',
          }"
          id="jobSummary"
        >
          <v-layout justify-space-between align-center>
            <span class="form-title-txt"> Key Information </span>
            <div class="form-card-status-indicator" v-if="!jobDetails.jobId">
              <i class="far fa-check"></i>
            </div>
            <v-spacer></v-spacer>
            <span>
              <v-tooltip right>
                <template v-slot:activator="{ on }">
                  <v-btn
                    class="edit-icons-btn"
                    flat
                    v-on="on"
                    icon
                    @click="keyDetailsDialogIsOpen = true"
                  >
                    <v-icon size="20">edit</v-icon>
                  </v-btn>
                </template>
                Edit
              </v-tooltip>
            </span>
          </v-layout>
          <div :class="{ scrolled: scrollY > setTopBarScroll }">
            <JobBookingKeyDetailsSummary
              :clientId="selectedClientDetails.clientId"
              :clientDetails="selectedClientDetails"
              :jobDetails="jobDetails"
              :jobDate="jobDate"
              :scrollY="scrollY"
            ></JobBookingKeyDetailsSummary>
            <!-- Job Action buttons - Floating ICON -->
            <div class="icon-btn-container" v-if="scrollY > setTopBarScroll">
              <JobActionButton
                v-for="(button, index) in actionButtons"
                :key="index"
                :icon="button.icon"
                :label="button.label"
                :disabled="button.disabled"
                @click="button.action"
                :isIcon="true"
              ></JobActionButton>
              <v-tooltip bottom>
                <template v-slot:activator="{ on }">
                  <v-btn
                    :class="{ visible: scrollY > setTopBarScroll }"
                    class="icon-action-btn scroll-up"
                    icon
                    v-on="on"
                    @click="scrollToTop()"
                    ><v-icon>arrow_upward</v-icon></v-btn
                  >
                </template>
                Scroll to top
              </v-tooltip>
            </div>
          </div>
          <!-- EDIT JOB DETAILS DIALOG -->
          <ContentDialog
            :showDialog.sync="keyDetailsDialogIsOpen"
            title="Book job"
            width="75%"
            contentPadding="pa-0"
            @cancel="cancelChanges"
            :showActions="false"
            :isConfirmUnsaved="false"
            :isDisabled="false"
            :isLoading="false"
          >
            <JobBookingKeyDetailsDialog
              :clientId="clientId"
              :clientDetails="selectedClientDetails"
              :jobDetails="jobDetails"
              :bookingData="bookingData"
              :isDialogOpen.sync="keyDetailsDialogIsOpen"
              :jobDate="jobDate"
              @applyChanges="copyKeyDetailsToJob"
              @update:isDialogOpen="closeDialog"
              :canEditForm="true"
            ></JobBookingKeyDetailsDialog>
          </ContentDialog>
        </v-flex>
        <!-- NOTES CARD -->
        <v-flex
          md4
          class="form-card"
          v-if="
            recurrenceType !== JobRecurrenceType.PERMANENT
              ? scrollY < 240
              : scrollY < 340
          "
        >
          <v-layout justify-space-between class="notes-header">
            <span class="form-title-txt"> Notes</span>
            <span class="form-title-txt"
              >( {{ jobDetails.notes.length }} )</span
            >
          </v-layout>
          <v-layout md12 mt-1 class="notes-list-container custom-scrollbar">
            <NotesList
              :isBookingScreen="true"
              :communications="jobDetails.notes"
              :showVisibilityTypeName="true"
              :allowDelete="!jobDetails.jobId"
              @removeNote="handleRemoveNote"
            >
            </NotesList>
          </v-layout>
        </v-flex>
        <!-- JOB ACTION BUTTONS CARD -->
        <v-flex md2 class="form-card action-btns">
          <v-flex>
            <JobActionButton
              v-for="(button, index) in actionButtons"
              :key="index"
              :icon="button.icon"
              :label="button.label"
              :disabled="button.disabled"
              @click="button.action"
              :isIcon="false"
            ></JobActionButton>
          </v-flex>
        </v-flex>
      </v-layout>
      <!-- PERMANENT JOB DETAILS -->
      <v-layout
        row
        wrap
        class="form-section"
        v-if="
          recurrenceType === JobRecurrenceType.PERMANENT && recurrenceDetails
        "
      >
        <v-flex
          md12
          class="form-card"
          :class="{
            'client-portal': sessionManager.isClientPortal(),
            highlight: highlight === 'recurrence',
          }"
          id="recurrence"
        >
          <v-layout justify-space-between align-center>
            <span class="form-title-txt">Recurrence Details</span>
            <div
              class="form-card-status-indicator"
              v-if="
                !!recurrenceDetails.nextScheduledRecurrence && !jobDetails.jobId
              "
            >
              <i class="far fa-check"></i>
            </div>
            <v-spacer></v-spacer>
            <span>
              <v-tooltip right>
                <template v-slot:activator="{ on }">
                  <v-btn
                    class="edit-icons-btn"
                    flat
                    v-on="on"
                    icon
                    @click="recurrenceDialogIsOpen = true"
                  >
                    <v-icon size="24">edit</v-icon>
                  </v-btn>
                </template>
                Edit
              </v-tooltip>
            </span>
          </v-layout>
          <RecurrenceVisualisation :recurringJobDetails="recurrenceDetails">
          </RecurrenceVisualisation>
          <JobBookingRecurrenceDialog
            :recurrenceDetails="recurrenceDetails"
            :isDialogOpen.sync="recurrenceDialogIsOpen"
            @applyChanges="recurrenceDetails = $event"
          >
          </JobBookingRecurrenceDialog>
        </v-flex>
      </v-layout>
      <!-- DELIVERY INFORMATION -->
      <v-layout row wrap class="form-section">
        <v-flex
          md12
          class="form-card second"
          :class="{
            'client-portal': sessionManager.isClientPortal(),
            highlight: highlight === 'deliverySummary',
          }"
          id="deliverySummary"
        >
          <div :class="{ 'pud-header-scrolled': scrollY > setTopBarScroll }">
            <div class="delivery-header">
              <div justify-start class="form-title-txt">
                Delivery Information
              </div>
              <div
                class="form-card-status-indicator"
                v-if="jobDetails.pudItems.length > 0 && !jobDetails.jobId"
              >
                <i class="far fa-check"></i>
              </div>
              <v-spacer></v-spacer>
              <div class="pud-total-txt">
                <span>
                  Pickups:
                  <span class="pud-count">{{
                    jobDetails.pudItems.filter(
                      (item) => item.legTypeFlag === 'P',
                    ).length
                  }}</span>
                </span>
                <span>
                  Drops:
                  <span class="pud-count">{{
                    jobDetails.pudItems.filter(
                      (item) => item.legTypeFlag === 'D',
                    ).length
                  }}</span>
                </span>
                <span>
                  Total:
                  <span class="pud-count">{{
                    jobDetails.pudItems.length
                  }}</span>
                </span>
              </div>
              <div class="btn-container" v-if="enableAddLegButtons">
                <v-btn
                  solo
                  depressed
                  class="quick-add-btn"
                  @click="quickAddSelectedPudIds"
                  v-if="unusedStagedUnassignedPudIds.length"
                  >Quick Add
                  {{ unusedStagedUnassignedPudIds.length }} Selected</v-btn
                >
                <span>
                  <v-tooltip
                    right
                    v-if="bookingData?.unassignedPudItems?.length"
                  >
                    <template v-slot:activator="{ on }">
                      <v-btn
                        class="addleg-icon-btn"
                        flat
                        v-on="on"
                        icon
                        @click="setParamsForFirstDialog"
                      >
                        <v-icon size="18">fas fa-route</v-icon>
                      </v-btn>
                    </template>
                    Add Legs from Point Manager
                  </v-tooltip>
                </span>
                <span>
                  <v-tooltip right>
                    <template v-slot:activator="{ on }">
                      <v-btn
                        class="add-icons-btn"
                        flat
                        v-on="on"
                        icon
                        @click="
                          addNewStop(jobDetails.pudItems.length < 1 ? 'P' : 'D')
                        "
                      >
                        <v-icon size="24">add</v-icon>
                      </v-btn>
                    </template>
                    ADD PICKUP/DROP
                  </v-tooltip>
                </span>
              </div>
            </div>
            <!-- PUD PILLS -->
            <PudPillsBar
              v-if="jobDetails.pudItems.length > 0"
              :jobDetails="jobDetails"
              :jobBookingDeliverySummaryRef="jobBookingDeliverySummary"
            ></PudPillsBar>
          </div>
          <!-- PUD SUMMARY -->
          <div :class="{ scrolling: scrollY > setTopBarScroll }">
            <JobBookingDeliverySummary
              ref="jobBookingDeliverySummary"
              :jobDetails="jobDetails"
              :selectedClientDetails="selectedClientDetails"
              :bookingData="bookingData"
              @editStop="editSelectedStop"
              @openStopForMatching="setParamsForSecondDialog"
            ></JobBookingDeliverySummary>
            <v-flex md12>
              <v-layout justify-center mt-2>
                <v-btn
                  depressed
                  large
                  solo
                  block
                  class="v-btn-custom pickup"
                  :disabled="
                    formDisabled ||
                    !jobDetails.client.id ||
                    !enableAddLegButtons ||
                    !enableAddPickupButton
                  "
                  @click="addNewStop('P')"
                >
                  {{ !lastLegIsPreload ? 'Add Pickup' : 'N/A (Preloaded)' }}
                </v-btn>
                <v-btn
                  depressed
                  large
                  solo
                  block
                  class="v-btn-custom drop"
                  :disabled="
                    jobDetails.pudItems.length < 1 || !enableAddLegButtons
                  "
                  @click="addNewStop('D')"
                >
                  {{ !lastLegIsPreload ? 'Add Delivery' : 'N/A (Preloaded)' }}
                </v-btn>
              </v-layout>
            </v-flex>
          </div>
          <!-- EDIT PUD SUMMARY -->
          <JobBookingAddStopDialog
            ref="jobBookingAddStopDialog"
            v-if="selectedPudItem"
            :key="selectedPudItem.uniqueId"
            :jobDetails="jobDetails"
            :pudItem="selectedPudItem"
            :clientDetails="selectedClientDetails"
            :isDialogOpen.sync="addStopDialogController"
            :formDisabled="formDisabled || loadingKeyData"
            :recurrenceType="recurrenceType"
            :jobDate="jobDate"
            @savePudItem="addOrUpdatePudItem"
            @deletePudAtIndex="deletePudItem"
          ></JobBookingAddStopDialog>
          <!-- RE-ORDER LEGS -->
          <JobBookingRouteDialog
            :type="routeDialogViewType"
            :isDialogOpen.sync="routeDialogIsOpen"
            :jobDetails="jobDetails"
            @reorderPudItems="updateJobForReorderedStops"
          >
          </JobBookingRouteDialog>

          <UnassignedPudItemDialog
            v-if="
              selectedClientDetails?.clientId &&
              bookingData?.unassignedPudItems?.length &&
              unassignedPudDialogParams?.showUnassignedPudDialog
            "
            v-bind="unassignedPudDialogParams"
            @closeDialog="closeUnassignedPudDialog"
            @addPudItemsFromUnassigned="addPudItemsFromUnassigned"
            @linkSelectedPointList="linkMultipleUnassignedPudFromId"
          />
        </v-flex>
      </v-layout>
      <!-- PRICING -->
      <v-layout
        class="form-section"
        v-if="
          bookingData?.currentServiceRates?.clientServiceRate &&
          bookingData?.currentFuelSurcharge
        "
      >
        <v-flex
          class="form-card"
          :class="{ highlight: highlight === 'pricing' }"
        >
          <v-layout justify-space-between align-center>
            <span class="form-title-txt">Pricing</span>
            <div
              class="form-card-status-indicator"
              v-if="
                !!jobDetails.rateTypeId &&
                !!jobDetails.serviceTypeId &&
                !jobDetails.jobId
              "
            >
              <i class="far fa-check"></i>
            </div>
            <div
              v-if="
                !sessionManager.isClientPortal() &&
                !!jobDetails.rateTypeId &&
                !!jobDetails.serviceTypeId &&
                !!jobDetails.accounting?.clientRates?.[0]?.rate
              "
              class="px-2"
            >
              <InformationTooltip
                :right="true"
                tooltipType="info"
                maxWidth="auto"
                iconSize="18"
              >
                <AppliedRateDetails
                  slot="content"
                  :clientCommonAddresses="bookingData?.commonAddresses ?? []"
                  :isTripRate="jobDetails.rateTypeId === JobRateType.TRIP"
                  :appliedRate="jobDetails.accounting.clientRates[0].rate"
                />
              </InformationTooltip>
            </div>
            <v-spacer></v-spacer>
            <span v-if="sessionManager.getPortalType() === Portal.OPERATIONS">
              <v-tooltip right>
                <template v-slot:activator="{ on }">
                  <v-btn
                    class="add-icons-btn"
                    flat
                    v-on="on"
                    icon
                    @click="additionalChargeDialogIsOpen = true"
                  >
                    <v-icon size="24">add</v-icon>
                  </v-btn>
                </template>
                Add Charge
              </v-tooltip>
            </span>
            <span>
              <v-tooltip right>
                <template v-slot:activator="{ on }">
                  <v-btn
                    class="edit-icons-btn"
                    flat
                    v-on="on"
                    icon
                    @click="pricingDialogIsOpen = true"
                    :disabled="jobDetails.pudItems.length === 0"
                  >
                    <v-icon size="24">edit</v-icon>
                  </v-btn>
                </template>
                Edit
              </v-tooltip>
            </span>
          </v-layout>
          <JobBookingPricingSummary
            v-if="jobDetails.pudItems.length > 0 && jobDetails.accounting"
            :serviceTypeId="jobDetails.serviceTypeId"
            :accounting="jobDetails.accounting"
            :pudItems="jobDetails.pudItems"
            id="pricing"
          ></JobBookingPricingSummary>
          <div class="no-data-text" v-if="jobDetails.pudItems.length === 0">
            <span>Not Available. Please add a Pickup location.</span>
          </div>
          <!-- EDIT PRICING DIALOG -->
          <JobBookingPricingDialog
            v-if="jobDetails.pudItems.length > 0"
            :jobDetails="jobDetails"
            :clientDetails="selectedClientDetails"
            :serviceRates="bookingData.currentServiceRates.clientServiceRate"
            :rateVariations="bookingData.serviceRateVariations"
            :fuelSurcharge="bookingData.currentFuelSurcharge"
            :isDialogOpen.sync="pricingDialogIsOpen"
            :commonAddresses="bookingData?.commonAddresses ?? []"
            :recurrenceType="recurrenceType"
            @applyChanges="copyPricingDetailsToJob"
          ></JobBookingPricingDialog>
          <!-- ADDITIONAL CHARGE DIALOG -->
          <AddAdditionalCharge
            :isDialogOpen.sync="additionalChargeDialogIsOpen"
            :chargeItems="rootStore.additionalChargeItemList"
            :currentAppliedCharges="
              jobDetails.accounting.additionalCharges.chargeList
            "
            :showClientFuelApplied="
              jobDetails.accounting.clientRates?.[0]?.rate
                .isClientFuelApplied ?? true
            "
            @addChargesToJob="addAdditionalChargesToJob"
          ></AddAdditionalCharge>
        </v-flex>
      </v-layout>

      <ClientClosedDialog
        :clientClosedDialogIsActive.sync="clientClosedDialogIsActive"
        @newJob="
          emit('finishedEditing', {
            confirmDiscard: false,
          })
        "
      ></ClientClosedDialog>
    </v-flex>
  </v-layout>

  <v-flex v-else>
    <v-layout justify-center align-center>
      <img
        src="@/static/loader/infinity-loader-light.svg"
        height="90px"
        width="90px"
      />
    </v-layout>
    <v-layout justify-center align-center class="mt-1"
      >Loading data, please wait...</v-layout
    >
  </v-flex>
</template>

<script setup lang="ts">
export interface JobBookingFormOptions {
  unassignedPudIds?: string[];
}

import JobBookingAddStopDialog, {
  SavePudPayload,
} from '@/components/booking/delivery_details/job_booking_add_stop_dialog.vue';
import JobBookingDeliverySummary from '@/components/booking/delivery_details/job_booking_delivery_summary.vue';
import JobBookingRouteDialog, {
  ReorderPudPayload,
  RouteDialogViewType,
} from '@/components/booking/delivery_details/job_booking_route_dialog.vue';
import PudPillsBar from '@/components/booking/delivery_details/pud_pills_bar.vue';
import JobBookingFormTopBar from '@/components/booking/job_booking_form_top_bar.vue';
import JobBookingSideSummary from '@/components/booking/job_booking_side_summary.vue';
import JobBookingKeyDetailsDialog, {
  JobKeyDetailsUpdate,
} from '@/components/booking/key_details/job_booking_key_details_dialog.vue';
import JobBookingKeyDetailsSummary from '@/components/booking/key_details/job_booking_key_details_summary.vue';
import JobBookingPricingDialog, {
  JobPricingDetailsUpdate,
} from '@/components/booking/pricing_details/job_booking_pricing_dialog.vue';
import JobBookingPricingSummary from '@/components/booking/pricing_details/job_booking_pricing_summary.vue';
import JobBookingRecurrenceDialog from '@/components/booking/recurrence/job_booking_recurrence_dialog.vue';
import AddAdditionalCharge from '@/components/common/additional_charge/add_additional_charge.vue';
import AppliedRateDetails from '@/components/common/applied_rate_details.vue';
import NotesEditor from '@/components/common/notes_editor/notes_editor.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import RecurrenceVisualisation from '@/components/common/recurring_job/recurrence_visualisation.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import ClientClosedDialog from '@/components/operations/BookJob/client_closed_dialog/index.vue';
import UnassignedPudItemDialog from '@/components/operations/BookJob/unassigned_pud_item_dialog/index.vue';
import { updatePudOrderWithOriginalTime } from '@/helpers/BookingHelpers/BookingHelpers';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { initialisePudItem } from '@/helpers/classInitialisers/InitialisePudItem';
import {
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { setCashSaleClientDetails } from '@/helpers/JobBooking/JobBookingClientHelpers';
import {
  hasNewPreloadDropoff,
  referenceListContainsPreloadReference,
} from '@/helpers/JobBooking/JobBookingPreloadHelpers';
import { estimateClientTotals } from '@/helpers/JobBooking/JobBookingPricingHelpers';
import {
  outsideMetroChargeApplies,
  returnAddedOrRemovedUnassignedPudIds,
  returnCurrentAssociatedUnassignedPudIds,
  updatePudListWithUnassignedPuds,
} from '@/helpers/JobBooking/JobBookingPudHelpers';
import { applyQuoteDetailsToJobDetails } from '@/helpers/JobBooking/JobBookingQuoteHelpers';
import { routeIsValidForJobLength } from '@/helpers/JobBooking/JobBookingRouteHelpers';
import { removeActionRequiredStatus } from '@/helpers/JobBooking/JobBookingStatusHelpers';
import { applyNewJobToJobDetails } from '@/helpers/JobBooking/JobReBookingHelpers';
import {
  addUnassignedPudJobReference,
  mergePudItemDetailsWithUnassigned,
  pudDataIsValid,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { tollAdminAndServicesFeeHandler } from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { isDistanceRateData } from '@/helpers/RateHelpers/RateDataHelpers';
import { isZoneToZoneRateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import { addOrReplaceRateTableItems } from '@/helpers/RateHelpers/ServiceRateHelpers';
import { requestZoneToZoneRatesForPudItems } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { ClientJobBookingData } from '@/interface-models/Booking/ClientJobBookingData';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { ClientOperationalStatusUpdate } from '@/interface-models/Client/ClientOperationalStatusUpdate';
import { returnAdditionalAccountingData } from '@/interface-models/Generic/Accounting/AdditionalAccountingData';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import AddAttachmentToJob from '@/interface-models/Generic/Attachment/AddAttachmentToJob';
import { AddNoteToJobRequest } from '@/interface-models/Generic/Communication/AddNoteToJobRequest';
import { Communication } from '@/interface-models/Generic/Communication/Communication';
import { JobNoteLevel } from '@/interface-models/Generic/Communication/JobNoteLevel';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Portal } from '@/interface-models/Generic/Portal';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import {
  JobBookingType,
  JobSuccessDialogConfig,
} from '@/interface-models/Jobs/JobBookingType';
import type { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import { JobRecurrenceType } from '@/interface-models/Jobs/JobRecurrenceType';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { PUDItem } from '@/interface-models/Jobs/PUD/PUDItem';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import { UnassignedPudJobDetailsResponse } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudJobDetailsRequest';
import { UpiAddUnassignedPuds } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UpiSelection';
import { RecurringJobTemplate } from '@/interface-models/Jobs/RecurringJob/RecurringJobTemplate';
import { RecurringJobDetails } from '@/interface-models/Jobs/RecurringJobDetails';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { ZoneToZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateType';
import { PudStatusUpdate } from '@/interface-models/Status/PudStatusUpdate';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useJobBookingStore } from '@/store/modules/JobBookingStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import { useMittListener } from '@/utils/useMittListener';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  nextTick,
  onMounted,
  Ref,
  ref,
  toRef,
  watch,
  WritableComputedRef,
} from 'vue';
import JobActionButton from './job_action_buttons.vue';

// In ClientPortal, clientId and clientDetails will be passed as props and the
// Client selection will be disabled.
const props = withDefaults(
  defineProps<{
    clientId?: string | null;
    clientDetails?: ClientDetails | null;
    jobDetails: JobDetails;
    isDialogOpen: boolean;
    scrollY: number;
    options?: JobBookingFormOptions;
  }>(),
  {
    clientId: null,
    clientDetails: null,
    scrollY: 0,
    options: undefined,
  },
);

interface ActionButton {
  icon: string;
  label: string;
  action: () => void;
  disabled: boolean;
  availableViews: JobRecurrenceType[];
  availablePortals: Portal[];
}

defineExpose({
  copyKeyDetailsToJob,
});

const emit = defineEmits<{
  (
    event: 'finishedEditing',
    payload: {
      confirmDiscard: boolean;
      successDialogConfig?: JobSuccessDialogConfig;
      jumpToDashboard?: boolean;
    },
  ): void;
  (event: 'clearOptions', payload: void): void;
}>();

const highlight: Ref<string> = ref('');
const noteDialogIsOpen: Ref<boolean> = ref(false);
const clientDetailsStore = useClientDetailsStore();
const jobStore = useJobStore();
const operationsStore = useOperationsStore();
const dataImportStore = useDataImportStore();
const jobBookingStore = useJobBookingStore();
const rootStore = useRootStore();
const clientPortalStore = useClientPortalStore();
const jobDetails: Ref<JobDetails> = toRef(props, 'jobDetails');
const bookingData: Ref<ClientJobBookingData | null> = ref(null);
const jobDate: Ref<number> = ref(0);

const jobBookingDeliverySummary: Ref<any> = ref({});
const loadingKeyData: Ref<boolean> = ref(true);
const awaitingApprovalResponse: Ref<boolean> = ref(false);
const awaitingSaveResponse: Ref<boolean> = ref(false);

const selectedClientDetails: Ref<ClientDetails> = ref(new ClientDetails());
const keyDetailsDialogIsOpen: Ref<boolean> = ref(false);

const selectedPudItem: Ref<PUDItem | null> = ref(null);
const addStopDialogIsOpen: Ref<boolean> = ref(false);

const routeDialogIsOpen: Ref<boolean> = ref(false);
const routeDialogViewType: Ref<RouteDialogViewType> = ref(
  RouteDialogViewType.ROUTE,
);

const recurrenceType: Ref<JobRecurrenceType> = ref(JobRecurrenceType.ADHOC);
const recurrenceDetails: Ref<RecurringJobDetails | null> = ref(null);
const recurrenceDialogIsOpen: Ref<boolean> = ref(false);

const pricingDialogIsOpen: Ref<boolean> = ref(false);
const additionalChargeDialogIsOpen: Ref<boolean> = ref(false);

const clientClosedDialogIsActive: Ref<boolean> = ref(false);

const jobBookingAddStopDialog: Ref<any> = ref(null);
const isDispatchNote: Ref<boolean> = ref(false);
// Unassigned puds
const currentUnassignedPudIds: Ref<string[]> = ref([]);

const setTopBarScroll = computed(() =>
  recurrenceType.value === JobRecurrenceType.ADHOC
    ? 240
    : recurrenceDetails.value?.nextScheduledRecurrence
      ? 620
      : 360,
);

const actionButtons: ComputedRef<ActionButton[]> = computed(() => {
  return [
    {
      icon: 'note_add',
      label: 'Add Note',
      disabled: noteDialogIsOpen.value,
      action: () => (
        (isDispatchNote.value = false),
        (noteDialogIsOpen.value = !noteDialogIsOpen.value)
      ),
      availableViews: [JobRecurrenceType.ADHOC, JobRecurrenceType.PERMANENT],
      availablePortals: [Portal.OPERATIONS, Portal.CLIENT],
    },
    {
      icon: 'reorder',
      label: 'Re-order Legs',
      action: openReorderLegsDialog,
      disabled: lastLegIsPreload.value,
      availableViews: [JobRecurrenceType.ADHOC, JobRecurrenceType.PERMANENT],
      availablePortals: [Portal.OPERATIONS, Portal.CLIENT],
    },
    {
      icon: 'pin_drop',
      label: 'View on Map',
      action: openViewRouteDialog,
      disabled: false,
      availableViews: [JobRecurrenceType.ADHOC, JobRecurrenceType.PERMANENT],
      availablePortals: [Portal.OPERATIONS, Portal.CLIENT],
    },
    {
      icon: 'paid',
      label: 'Add Charge',
      action: () => (additionalChargeDialogIsOpen.value = true),
      disabled: false,
      availableViews: [JobRecurrenceType.ADHOC, JobRecurrenceType.PERMANENT],
      availablePortals: [Portal.OPERATIONS],
    },
    {
      icon: 'far fa-clipboard',
      label: 'Dispatch Note',
      action: () =>
        addNoteToJob(jobDetails.value.jobId ?? 0, false, false, true),
      disabled: false,
      availableViews: [JobRecurrenceType.ADHOC, JobRecurrenceType.PERMANENT],
      availablePortals: [Portal.OPERATIONS],
    },
  ].filter(
    (b) =>
      b.availableViews.includes(recurrenceType.value) &&
      b.availablePortals.includes(sessionManager.getPortalType()),
  );
});

/**
 * Watches changes to jobDetails.value for isDirectToInvoice.
 * If PERMANENT Job:
 *  Checks if pudItems is not more or less than 2 or if additionalCharges.chargeList is not empty.
 *  sets isDirectToInvoice false.
 *  Uses deep watching to track nested object changes.
 */
watch(
  () => jobDetails.value,
  (newValue, oldValue) => {
    if (recurrenceType.value === JobRecurrenceType.PERMANENT) {
      if (
        newValue.pudItems.length !== 2 ||
        newValue.accounting.additionalCharges.chargeList.length !== 0
      ) {
        if (jobDetails.value.isDirectToInvoice) {
          oldValue.isDirectToInvoice = false;
          jobDetails.value.isDirectToInvoice = false;
        }
      }
    }
  },
  { deep: true },
);

function cancelChanges(value: boolean) {
  keyDetailsDialogIsOpen.value = value;
}

function closeDialog() {
  keyDetailsDialogIsOpen.value = false;
}
/**
 * Used in template to disable inputs when loading key data or when other
 * conditions are met.
 */
const formDisabled: ComputedRef<boolean> = computed(() => {
  return loadingKeyData.value;
});

/**
 * Used in template to disable the add pickup button. If the job rate type is
 * ZONE TO ZONE we only allow one pickup, so we disable the button if a pickup
 * already exists.
 */
const enableAddPickupButton: ComputedRef<boolean> = computed(() => {
  return (
    jobDetails.value.rateTypeId !== JobRateType.ZONE_TO_ZONE ||
    !jobDetails.value.pudItems.some((pud) => pud.legTypeFlag === 'P')
  );
});
const enableAddLegButtons: ComputedRef<boolean> = computed(() => {
  return !lastLegIsPreload.value;
});

/**
 * Controls visibility of JobBookingAddStopDialog component. Returns true when
 * selectedPudItem is not null, and sets selectedPudItem to null when set to
 * false from emit in JobBookingAddStopDialog.
 */
const addStopDialogController: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return addStopDialogIsOpen.value;
  },
  set(value: boolean): void {
    if (value) {
      nextTick(() => {
        addStopDialogIsOpen.value = value;
      });
    } else {
      selectedPudItem.value = null;
      addStopDialogIsOpen.value = value;
    }
  },
});

/**
 * Sets the incoming payload to the bookingData object, which contains various
 * key data required for booking a job.
 * @param data - Payload including service rates, fuel surcharge, unassigned
 * puds etc.
 */
async function setRequiredBookingData(data: ClientJobBookingData) {
  if (props.options?.unassignedPudIds?.length) {
    data.unassignedPudIds = [...props.options.unassignedPudIds];
    emit('clearOptions');
  }
  bookingData.value = data;
  await updateAdditionalDataIfRequired(
    jobDetails.value.accounting,
    bookingData.value?.currentServiceRates?.clientServiceRate,
    false,
  );
}

/**
 * Called when we add, delete or edit a stop on a job. Recalculate the
 * additional accounting data if the service rate table contains any DISTANCE
 * rate rateTableItems.
 * @param accounting - The accounting details to use to recompute the additional data for
 * @param clientServiceRate - The client service rate to check for distance
 */
async function updateAdditionalDataIfRequired(
  accounting: JobAccountingDetails,
  clientServiceRate?: ClientServiceRate | null,
  shouldResetEditedDistance: boolean = false,
) {
  if (!clientServiceRate) {
    return;
  }
  let applicableRateTypes: JobRateType[] = [];
  // Check if the client has ZONE TO ZONE
  const hasZoneToZone = clientServiceRate.rateTableItems.some(
    (x) => x.rateTypeId === JobRateType.ZONE_TO_ZONE,
  );
  if (hasZoneToZone) {
    applicableRateTypes.push(JobRateType.ZONE_TO_ZONE);
  }

  // Check if the service rate table contains any DISTANCE rate rateTableItems
  const hasDistanceItems = clientServiceRate.rateTableItems.some(
    (x) => x.rateTypeId === JobRateType.DISTANCE,
  );
  if (hasDistanceItems) {
    applicableRateTypes.push(JobRateType.DISTANCE);
  }
  if (applicableRateTypes.length) {
    // Computed the additional data object
    accounting.additionalData = await returnAdditionalAccountingData({
      jobDetails: jobDetails.value,
      applicableRateTypes: applicableRateTypes,
      forceRequest: true,
    });
    // Reset the edited distance rate if it exists
    if (
      shouldResetEditedDistance &&
      accounting.additionalData.distanceRate?.chargeableClientDistance.edited
    ) {
      if (
        isDistanceRateData(
          accounting.clientRates?.[0]?.rate?.rateTypeId,
          accounting.finishedJobData.clientRateData,
        )
      ) {
        const rateData = accounting.finishedJobData.clientRateData;
        if (
          !!rateData.uneditedTravelDistance &&
          rateData.uneditedTravelDistance !==
            accounting.additionalData.distanceRate.chargeableClientDistance
              .edited
        ) {
          showAppNotification(
            'The route has been updated - the current custom distance has been reset. Please review in the pricing section.',
            HealthLevel.WARNING,
          );
        }
      }
      accounting.additionalData.distanceRate.chargeableClientDistance.edited =
        undefined;
    }
  }
}

/**
 * Sets properties in the provided payload to their respective properties in the
 * jobDetails object. Called in 2 ways:
 *  - Emitted from JobBookingKeyDetailsDialog when selecting a client.
 *  - Called through exposed method from parent component (using defineExpose),
 *    when booking a new job.
 * @param details - Properties to set to job, as well as other required data
 * that we will use throughout the components
 */
async function copyKeyDetailsToJob(details: JobKeyDetailsUpdate) {
  selectedClientDetails.value = details.clientDetails;
  jobDetails.value.client = details.clientShort;
  jobDetails.value.clientDispatcher = details.clientDispatcher;
  jobDetails.value.proofOfDelivery = details.proofOfDelivery;
  jobDetails.value.additionalEquipments = details.additionalEquipmentList;
  jobDetails.value.jobReference = details.jobReferences;
  jobDetails.value.notes = details.jobNotes;
  jobDetails.value.cashSaleClientDetails = details.cashSaleClientDetails;
  jobDate.value = details.jobDate;

  if (details.quoteDetails) {
    applyQuoteDetailsToJobDetails(jobDetails.value, details.quoteDetails);
  }

  if (details.jobDetails) {
    applyNewJobToJobDetails(jobDetails.value, details.jobDetails);
  }

  const firstPudEpochTime = moment
    .tz(
      returnFormattedDate(details.jobDate, 'DDMMYYYY') +
        ' ' +
        details.firstPudArrival,
      'DDMMYYYY HHmm',
      useCompanyDetailsStore().userLocale,
    )
    .valueOf();

  if (jobDetails.value.pudItems.length > 0) {
    jobDetails.value.pudItems[0].epochTime = firstPudEpochTime;
  }
  // If we added at least 2 puds from the quote/recentJob, then request the planned
  // route
  if (jobDetails.value.pudItems.length >= 2) {
    const updatedRoute = await jobDetails.value.getPlannedRoute();
    updateJobRouteWithIncoming(updatedRoute ?? undefined);
  }

  // Update the recurrenceType if it was provided. This will only be updated in
  // the case of a new job.
  if (details.recurrenceType) {
    recurrenceType.value = details.recurrenceType;
    recurrenceDetails.value =
      recurrenceType.value === JobRecurrenceType.PERMANENT
        ? new RecurringJobDetails()
        : null;
  }
  if (recurrenceType.value === JobRecurrenceType.ADHOC) {
    recurrenceDetails.value = null;
  }

  if (jobDetails.value.pudItems.length > 0) {
    if (jobDetails.value.pudItems[0].pickupDate !== details.jobDate) {
      jobDetails.value.pudItems[0].timeDefinition = 0;
    }
  }

  await setRequiredBookingData(details.bookingData);
}

/**
 * Sets properties in the provided payload to their respective properties in the
 * jobDetails object. Called in 2 ways:
 *  - Emitted from JobBookingKeyDetailsDialog when selecting a client.
 *  - Called through exposed method from parent component (using defineExpose),
 *    when booking a new job.
 * @param details - Properties to set to job, as well as other required data
 * that we will use throughout the components
 */
function copyPricingDetailsToJob(details: JobPricingDetailsUpdate) {
  jobDetails.value.serviceTypeId = details.serviceTypeId;
  jobDetails.value.rateTypeId = details.rateTypeId;
  jobDetails.value.pudItems = details.pudItems;
  jobDetails.value.isDirectToInvoice = details.directToInvoicing;

  recalculateAccountingTotals(details.accounting);
}

/**
 * Handles emit from AddAdditionalCharge component to add a list of charges to the
 * job. Adds the full objects and the ids to the accounting details, then
 * recalculates the toll admin and handling charge if the new charge is a toll
 * charge.
 * @param items The new charges to add to the job
 *
 */
function addAdditionalChargesToJob(items: AdditionalChargeItem[]) {
  if (!items || items.length === 0) {
    return;
  }

  // Iterate over the items and add them to the jobDetails object.
  items.forEach((item: AdditionalChargeItem) => {
    // Check if the charge already exists in the list.
    const foundExistingCharge =
      jobDetails.value.accounting.additionalCharges.chargeList.find(
        (x) => x._id === item._id,
      );

    // If it does, increment the quantity. If not, add it to the list
    if (foundExistingCharge) {
      foundExistingCharge.quantity++;
    } else {
      jobDetails.value.accounting.additionalCharges.chargeList.push(item);
      jobDetails.value.accounting.additionalCharges.chargeIdList.push(
        item._id!,
      );
    }
  });

  // Check and see if the new charge is a toll item. If true we need to
  // recalculate our toll admin and handling charge
  const tollAndHandlingItem = rootStore.additionalChargeItemList.find(
    (x: AdditionalChargeItem) =>
      x._id === useRootStore().tollAdminAndHandlingId,
  );

  // Update the toll admin and handling charge in the job
  tollAdminAndServicesFeeHandler(
    jobDetails.value.accounting.additionalCharges,
    tollAndHandlingItem,
    rootStore.tollAdminAndHandlingId,
    rootStore.tollChargeTypeId,
  );

  recalculateAccountingTotals(jobDetails.value.accounting);
}

/**
 * Updates the accounting totals in the jobDetails object. This is called when
 * certain emits are triggered from child components which would effect the
 * accounting totals. The result of the calculations if set to the job.
 * @param accounting - The accounting details to use to calculate the new
 * totals.
 */
function recalculateAccountingTotals(accounting: JobAccountingDetails) {
  const updatedAccountingDetails = estimateTotalsFromAccountingDetails(
    jobDetails.value,
    accounting,
    jobDetails.value.pudItems,
  );
  if (updatedAccountingDetails) {
    jobDetails.value.accounting = updatedAccountingDetails;
  } else {
    showAppNotification('Error calculating totals. Please try again.');
  }
}

/**
 * Returns a boolean indicating whether the last leg of the job is a preload.
 * Used to disable the add pickup/drop buttons.
 */
const lastLegIsPreload: ComputedRef<boolean> = computed(() => {
  if (jobDetails.value.pudItems.length === 0) {
    return false;
  }
  const lastPud =
    jobDetails.value.pudItems[jobDetails.value.pudItems.length - 1];
  return (
    lastPud.legTypeFlag === 'D' &&
    referenceListContainsPreloadReference(lastPud.dropoffReference)
  );
});

/**
 * Called from action buttons to open the notes dialog
 * @param jobId - The jobId to add the note to
 * @param serviceFailure - If true, the note will be marked as a service failure
 * @param cancelJob - If true, the note will be marked as a job cancellation
 * @param dispatchNote - If true, the note will be marked as a dispatch note
 * @param startOfDayCheckNote - If true, the note will be marked as a start of
 */
function addNoteToJob(
  jobId: number,
  serviceFailure: boolean = false,
  cancelJob: boolean = false,
  dispatchNote: boolean = false,
  startOfDayCheckNote: boolean = false,
): void {
  if (jobId !== 0) {
    operationsStore.setSelectedJobId(jobId);
    operationsStore.setViewingJobNotesDialog(true);
    operationsStore.setJobServiceFailure(serviceFailure);
    operationsStore.setJobCancellation(cancelJob);
    operationsStore.setDispatchNote(dispatchNote);
    operationsStore.setStartOfDayCheckNote(startOfDayCheckNote);
  } else {
    isDispatchNote.value = true;
    noteDialogIsOpen.value = !noteDialogIsOpen.value;
  }
}

// Calculates the totals and other values previously held by
// FinalisedJobDetails object
function estimateTotalsFromAccountingDetails(
  job: JobDetails,
  accounting: JobAccountingDetails,
  pudItems: PUDItem[],
): JobAccountingDetails | null {
  // If there's no rate information, then return the original accounting details
  if (!accounting.clientRates || accounting.clientRates.length === 0) {
    return accounting;
  }
  const chargeTypeList = useRootStore().additionalChargeTypeList ?? [];
  const outsideMetroAreaHit: boolean = outsideMetroChargeApplies(
    useCompanyDetailsStore().insideMetroSuburbs,
    pudItems,
  );
  const clientOutsideMetroRate =
    accounting.clientRates[0]?.outsideMetroRate &&
    outsideMetroAreaHit &&
    accounting.clientRates[0].rate.rateTypeId === JobRateType.TIME
      ? accounting.clientRates[0].outsideMetroRate
      : 0;

  accounting.clientRates[0].outsideMetroRate = clientOutsideMetroRate;
  return estimateClientTotals({
    jobDetails: job,
    pudItems,
    accounting,
    additionalChargeTypeList: chargeTypeList,
    clientOutsideMetroRate,
    commonAddresses: bookingData.value?.commonAddresses ?? [],
    chargeableDistance:
      accounting.additionalData?.distanceRate?.chargeableClientDistance?.edited,
    isClientPortal: sessionManager.isClientPortal(),
    divisionDetails: useCompanyDetailsStore().divisionDetails,
  });
}

/**
 * Adds a new stop to the job. Opens the JobBookingAddStopDialog with a new
 * instance of PUDItem, with the legFlagType set to the provided legType.
 * @param legType - The legType of the stop to add. 'P' for Pickup, 'D' for
 * Delivery.
 */
function addNewStop(legType: 'P' | 'D') {
  const pud = new PUDItem();
  if (legType === 'P') {
    pud.pickupReference = [new JobReferenceDetails()];
  } else {
    pud.dropoffReference = [new JobReferenceDetails()];
  }
  pud.legTypeFlag = legType;
  selectedPudItem.value = pud;
  addStopDialogController.value = true;
}

/**
 * Captures emit from JobBookingDeliverySummary to edit the selected stop. Finds
 * the associated PUDItem in jobDetails.pudItems and sets it to selectedPudItem,
 * which will open the dialog to edit it.
 * @param pudId - The PUDId of the stop to edit.
 */
function editSelectedStop(pudId: string) {
  // Find pudItem
  if (pudId) {
    const pudItem = jobDetails.value.pudItems.find(
      (item) => item.uniqueId === pudId,
    );
    // Set the selectedPudItem to the found pudItem
    if (pudItem) {
      selectedPudItem.value = initialisePudItem(pudItem);
      addStopDialogController.value = true;
    } else {
      showAppNotification(
        'Something went wrong. Could not find the selected stop',
      );
    }
  } else {
    selectedPudItem.value = null;
  }
}

/**
 * Captures emit from JobBookingDeliverySummary to open the
 * JobBookingAddStopDialog
 */
async function deletePudItem(index: number): Promise<void> {
  // Remove the PUD item at the specified index
  jobDetails.value.pudItems.splice(index, 1);

  // Re-request the route after PUD item change and update the job route
  const updatedRoute = await jobDetails.value.getPlannedRoute();
  updateJobRouteWithIncoming(updatedRoute ?? undefined);
  // We need to update the distance rate info whenever the puds change
  await updateAdditionalDataIfRequired(
    jobDetails.value.accounting,
    bookingData.value?.currentServiceRates?.clientServiceRate,
    true,
  );
  // Re-request zone to zone information (for zone to zone rate jobs only)
  await updateZoneToZoneRateTableItemsIfRequired();

  addStopDialogController.value = false;
  recalculateAccountingTotals(jobDetails.value.accounting);
}

/**
 * Handles emit from JobBookingAddStopDialog to add or update a PUDItem in the
 * job. Replaces the PUDItem in the jobDetails.pudItems array if it already
 * exists, otherwise adds it to the array. Also sets plannedRoute (and maybe
 * other fields) if provided in the payload.
 * @param payload - The payload containing the PUDItem to add or update, and
 * other optional fields.
 */
async function addOrUpdatePudItem(payload: SavePudPayload) {
  const { pudItem, plannedRoute } = payload;

  // Set pudItem to jobDetails.pudItems if it already exists, otherwise add it.
  const index = jobDetails.value.pudItems.findIndex(
    (item) => item.uniqueId === pudItem.uniqueId,
  );
  let routeHasChanged = false;
  if (index > -1) {
    // Update existing item in place, which maintains reactivity
    const removedItems = jobDetails.value.pudItems.splice(index, 1, pudItem); // This updates the item reactively
    const oldPudItem = removedItems[0] ?? undefined;
    if (oldPudItem) {
      routeHasChanged = didAddressChange(oldPudItem.address, pudItem.address);
    }
  } else {
    // If it doesn't exist, push the new item
    jobDetails.value.pudItems.push(pudItem);
    routeHasChanged = true;
  }

  // Update route, pud times and depot durations
  updateJobRouteWithIncoming(plannedRoute);

  // We need to update the distance rate info whenever the puds change
  await updateAdditionalDataIfRequired(
    jobDetails.value.accounting,
    bookingData.value?.currentServiceRates?.clientServiceRate,
    routeHasChanged,
  );

  // Reset selectedPudItem to null to indicate we're no longer editing
  selectedPudItem.value = null;

  // Re-request zone to zone information (for zone to zone rate jobs only)
  await updateZoneToZoneRateTableItemsIfRequired();

  recalculateAccountingTotals(jobDetails.value.accounting);

  nextTick(() => {
    if (payload?.newLegTypeFlag) {
      addNewStop(payload.newLegTypeFlag);
    }
  });
}

/**
 * Check if the address has changed. Used to check if we need to clear the
 * edited
 * @param oldAddress - The old address
 * @param newAddress - The new address
 */
function didAddressChange(
  oldAddress: AddressAU,
  newAddress: AddressAU,
): boolean {
  return (
    oldAddress.geoLocation[0] !== newAddress.geoLocation[0] ||
    oldAddress.geoLocation[1] !== newAddress.geoLocation[1]
  );
}

/**
 * Called when route is updated. Updates the plannedRoute in jobDetails and
 * recalculates pud times and depot durations.
 * @param route - The updated route.
 */
function updateJobRouteWithIncoming(route: ORSRoute | undefined) {
  // Set plannedRoute if provided
  jobDetails.value.plannedRoute = route ?? jobDetails.value.plannedRoute;

  // If we only have 1 stop, then return and do nothing
  if (jobDetails.value.pudItems.length < 2) {
    return;
  }

  // If the route is not valid for the job length, we cannot do anything more
  // with it
  if (
    !routeIsValidForJobLength(
      jobDetails.value.pudItems.length,
      jobDetails.value.plannedRoute,
    )
  ) {
    showAppNotification(
      'One or more legs has an invalid address. Please review all stops for missing address information.',
    );
    return;
  }
  // If the route is valid, then proceed to update pud times from travel times
  jobDetails.value.setPudTimesFromPlannedRoute(jobDetails.value.plannedRoute);

  // Update the depot durations (sends request and sets response to depot
  // durations within uncompletedJob)
  jobDetails.value.requestDepotDurations();
}

/**
 * Handles emit from JobBookingRouteDialog component. Updates the job details
 * with the re-ordered list of PUDItems and the updated route.
 * @param payload - The payload containing the re-ordered PUDItems and the updated route.
 */
async function updateJobForReorderedStops(payload: ReorderPudPayload) {
  if (!jobDetails.value.pudItems.length) {
    return;
  }
  const {
    epochTime: originalEpochTime,
    timeDefinition: originalTimeDefinition,
  } = jobDetails.value.pudItems[0];

  // Update the PUD items with the reordered list
  jobDetails.value.pudItems = payload.pudItems;

  // Restore the original time and time definition for the first PUD item if
  // changed
  updatePudOrderWithOriginalTime(
    jobDetails.value.pudItems,
    originalEpochTime,
    originalTimeDefinition,
  );
  // Update the planned route
  updateJobRouteWithIncoming(payload.plannedRoute);

  // Update additional data and zone-to-zone rate table items if required
  await Promise.all([
    updateAdditionalDataIfRequired(
      jobDetails.value.accounting,
      bookingData.value?.currentServiceRates?.clientServiceRate,
      true,
    ),
    updateZoneToZoneRateTableItemsIfRequired(),
  ]);

  // Recalculate accounting totals
  recalculateAccountingTotals(jobDetails.value.accounting);
}

/**
 * Opens the JobBookingKeyDetailsDialog, with the view type set to ROUTE (no
 * actions)
 */
function openViewRouteDialog() {
  routeDialogViewType.value = RouteDialogViewType.ROUTE;
  routeDialogIsOpen.value = true;
}

/**
 * Opens the JobBookingKeyDetailsDialog, with the view type set to REORDER
 * (re-order actions and actions bar visible)
 */
function openReorderLegsDialog() {
  routeDialogViewType.value = RouteDialogViewType.REORDER;
  routeDialogIsOpen.value = true;
}

/**
 * Sets the ClientDetails according to the props passed to the component, and
 * sets other initial data for use in the component.
 *
 *  - Option 1: clientDetails is passed as a prop. This happens on the Client
 *     Portal. Use this props.clientDetails to set the selectedClientDetails.
 *  - Option 2: props.clientDetails is not passed, and jobDetails already has a
 *    jobId. We are editing an existing job. Request client details from the API
 *    using the jobDetails.client.id.
 *  - Option 3: props.clientDetails is not passed, and jobDetails does not have
 *    a jobId. We are creating a new job. Use a new instance of ClientDetails,
 *    which we will replace when the user selects a client.
 */
async function setInitialData() {
  loadingKeyData.value = true;

  // Use clientDetails from props if available, otherwise use a new instance of ClientDetails.
  if (props.clientDetails) {
    selectedClientDetails.value = props.clientDetails;
  } else if (props.jobDetails.jobId) {
    if (!props.jobDetails.client.id) {
      return;
    }
    if (!props.jobDetails.isCashSale) {
      // If not a cash sale client, fetch client details from the API using the
      // jobDetails.client.id
      const clientDetails =
        await clientDetailsStore.requestClientDetailsByClientId(
          props.jobDetails.client.id,
        );

      if (clientDetails) {
        selectedClientDetails.value = clientDetails;
      } else {
        handleMissingData();
        selectedClientDetails.value = new ClientDetails();
      }
    } else {
      // Cash sale client
      selectedClientDetails.value = new ClientDetails();
      selectedClientDetails.value.clientId = 'CS';
      selectedClientDetails.value.clientName =
        props.jobDetails.client.clientName;
    }
  } else {
    selectedClientDetails.value = new ClientDetails();
  }

  if (
    (jobDetails.value.jobId || !!jobDetails.value.client.id) &&
    jobDetails.value.pudItems.length > 0
  ) {
    // Request client data if clientId is available (not a new job)
    if (selectedClientDetails.value.clientId) {
      await requestClientData(selectedClientDetails.value);
    }
    currentUnassignedPudIds.value = returnCurrentAssociatedUnassignedPudIds(
      jobDetails.value.pudItems,
    );
    // Re-request zone to zone information (for zone to zone rate jobs only)
    await updateZoneToZoneRateTableItemsIfRequired();

    // Check if there is an element in clientRates, and if there is
    // clientServiceRateVariations in the rate data response
    const rateTableItem = jobDetails.value.accounting?.clientRates?.[0]?.rate;
    if (rateTableItem && bookingData.value?.serviceRateVariations?.length) {
      // Set the appropriate rateVariation to the job
      jobDetails.value.accounting.clientServiceRateVariations =
        rateTableItem.getRateVariation(bookingData.value.serviceRateVariations);
    }

    // Recalculate accounting information
    recalculateAccountingTotals(jobDetails.value.accounting);
  }

  loadingKeyData.value = false;
}

/**
 * Called when the component mounts or when the puds are updated. If the job is
 * a ZONE TO ZONE rate type, requests the current zone to zone rate information
 * for the pud items.
 */
async function updateZoneToZoneRateTableItemsIfRequired() {
  const clientServiceRates =
    bookingData.value?.currentServiceRates?.clientServiceRate;
  if (
    !clientServiceRates?.rateTableItems.some(
      (x) => x.rateTypeId === JobRateType.ZONE_TO_ZONE,
    ) ||
    jobDetails.value.pudItems.length < 2 ||
    !jobDetails.value.serviceTypeId ||
    jobDetails.value.rateTypeId !== JobRateType.ZONE_TO_ZONE
  ) {
    return;
  }
  if (
    Array.isArray(
      jobDetails.value.accounting?.clientRates?.[0]?.rate?.rateTypeObject,
    ) &&
    jobDetails.value.accounting?.clientRates?.[0]?.rate?.rateTypeObject.length
  ) {
    jobDetails.value.accounting.clientRates[0].rate.rateTypeObject = [];
  }
  try {
    const zzRateTableItems = await requestZoneToZoneRatesForPudItems({
      type: RateEntityType.CLIENT,
      entityId: jobDetails.value.client.id,
      pudItems: jobDetails.value.pudItems,
      serviceTypeId: jobDetails.value.serviceTypeId,
      returnPartialResult: true,
    });
    if (!zzRateTableItems) {
      throw new Error('Zone to Zone rate response was empty');
    }
    // Add or replace the zone to zone rate table items in the service rates
    addOrReplaceRateTableItems(
      clientServiceRates.rateTableItems,
      zzRateTableItems,
    );

    // Validate response, then replace the rateTypeObject in the clientRates
    const clientRates = jobDetails.value.accounting?.clientRates?.[0]?.rate;
    if (
      clientRates &&
      isZoneToZoneRateTypeObject(
        clientRates.rateTypeId,
        clientRates.rateTypeObject,
      ) &&
      zzRateTableItems.length === 1 &&
      zzRateTableItems[0].serviceTypeId === clientRates.serviceTypeId
    ) {
      clientRates.rateTypeObject = zzRateTableItems[0]
        .rateTypeObject as ZoneToZoneRateType[];
    }
  } catch (error) {
    logConsoleError('Error updating zone to zone rate table items', error);
  }
}

// ===========================================================================
// UNASSIGNED PUD ITEM
// ===========================================================================
interface UnassignedPudItemDialogConfig {
  showUnassignedPudDialog: boolean;
  clientId: string;
  unassignedPudItemList: UnassignedPudItem[];
  currentStagedPudItemIds: string[];
  itemsForQuickAdd: string[];
  jobDetails: JobDetails | null;
  pudItems: PUDItem[];
  currentUnassignedPudIds: string[];
  isMatching: boolean;
  pudDetails: PUDItem | null;
  jobReferences: JobReferenceDetails[];
  selectedPudIdToLink: string | null;
  singlePudView: boolean;
}

const unassignedPudDialogParams: Ref<UnassignedPudItemDialogConfig | null> =
  ref(null);

/**
 * Opens the UnassignedPudItemDialog with the provided to the list view, so the
 * user can add new unassigned pud items to the job. Called from action button
 * when unassigned pud items are available. Sets the params for the dialog and
 * opens it.
 */
function setParamsForFirstDialog() {
  unassignedPudDialogParams.value = {
    showUnassignedPudDialog: true,
    clientId: selectedClientDetails.value.clientId!,
    unassignedPudItemList: bookingData.value?.unassignedPudItems || [],
    currentStagedPudItemIds: currentStagedUnassignedPudIds.value,
    itemsForQuickAdd: unusedStagedUnassignedPudIds.value,
    jobDetails: jobDetails.value,
    pudItems: jobDetails.value.pudItems,
    currentUnassignedPudIds: currentUnassignedPudIds.value,
    isMatching: false,

    pudDetails: null,
    jobReferences: [],
    selectedPudIdToLink: null,
    singlePudView: false,
  };

  unassignedPudDialogParams.value.showUnassignedPudDialog = true;
}

/**
 * Opens the UnassignedPudItemDialog with the provided pudId so that the user
 * can perform leg matching. Sets the params for the dialog and opens it. Called
 * from emit from JobBookingDeliverySummary.
 */
function setParamsForSecondDialog(pudId: string) {
  const pudItem = jobDetails.value.pudItems.find(
    (item) => !!item.pudId && item.pudId === pudId,
  );
  if (!pudItem?.pudId) {
    console.error('Could not open leg matching dialog for pudId: ', pudId);
    return;
  }
  unassignedPudDialogParams.value = {
    showUnassignedPudDialog: true,
    clientId: selectedClientDetails.value.clientId!,
    unassignedPudItemList: bookingData.value?.unassignedPudItems || [],
    currentStagedPudItemIds: [],
    itemsForQuickAdd: [],
    jobDetails: jobDetails.value,
    pudItems: jobDetails.value.pudItems,
    currentUnassignedPudIds: currentUnassignedPudIds.value,
    isMatching: true,
    pudDetails: pudItem,
    jobReferences: jobDetails.value.jobReference,
    selectedPudIdToLink: pudItem.pudId,
    singlePudView: true,
  };

  // Show dialog
  unassignedPudDialogParams.value.showUnassignedPudDialog = true;
}

/**
 * Closes the UnassignedPudItemDialog. Called from emit from the dialog. Sets
 * the params to null.
 */
function closeUnassignedPudDialog() {
  unassignedPudDialogParams.value = null;
}
/**
 * Return a list of the UPI.id's that are currently either in the job, or in
 * the list in config
 */
const currentStagedUnassignedPudIds: ComputedRef<string[]> = computed(() => {
  const fromConfig = bookingData.value?.unassignedPudIds ?? [];
  const currentlyAdded = returnCurrentAssociatedUnassignedPudIds(
    jobDetails.value.pudItems,
  );
  const combined = [...new Set(fromConfig.concat(currentlyAdded))];
  return combined;
});

/**
 * Return how many UPI's have been suggested to be added,
 * But have not yet been added to this job
 */
const unusedStagedUnassignedPudIds: ComputedRef<string[]> = computed(() => {
  const fromConfig: string[] = bookingData.value?.unassignedPudIds ?? [];
  const currentlyAdded = returnCurrentAssociatedUnassignedPudIds(
    jobDetails.value.pudItems,
  );
  return fromConfig.filter((id: string) => !currentlyAdded.includes(id));
});

/**
 * Called from button in template to add any 'staged' unassigned pud items to
 * the current job. This occurs when the user selects the job from the point
 * manager, which prepares a list of unassigned puds to add.
 */
function quickAddSelectedPudIds() {
  if (
    !bookingData.value?.unassignedPudItems ||
    !unusedStagedUnassignedPudIds.value.length
  ) {
    return;
  }
  const unassignedPudList = bookingData.value.unassignedPudItems;
  const ids: string[] = unusedStagedUnassignedPudIds.value;
  const pudItems: PUDItem[] = [];
  const groupIds: string[] = [];
  ids.forEach((id: string) => {
    const foundMatch = unassignedPudList.find((upi) => upi.id === id);
    if (foundMatch) {
      groupIds.push(foundMatch.clientSuppliedId);
      pudItems.push(initialisePudItem(foundMatch.asPudItem()));
    }
  });

  const upiAddUnassignedPuds: UpiAddUnassignedPuds = {
    groupIds: [...new Set(groupIds)],
    pudItems,
  };
  addPudItemsFromUnassigned(upiAddUnassignedPuds);
}

/**
 * Captures emit from Unassigned Pud List dialog
 * Adds the pudItems constructed in dialog to the current jobs pudList
 * @param incomingPudItems - The pudItems to add to the job
 */
async function addPudItemsFromUnassigned(
  incomingPudItems: UpiAddUnassignedPuds,
) {
  jobDetails.value.pudItems = updatePudListWithUnassignedPuds(
    jobDetails.value.pudItems,
    incomingPudItems,
  );

  // we should now add our groupIds (client supplied reference on the unassigned pud) as job references.
  for (const groupId of incomingPudItems.groupIds) {
    addUnassignedPudJobReference(groupId, jobDetails.value.jobReference);
  }

  closeUnassignedPudDialog();

  // Re-request the route now that we've added new stops
  const updatedRoute = await jobDetails.value.getPlannedRoute();
  updateJobRouteWithIncoming(updatedRoute ?? undefined);
}

// Capture emit from Unassigned Pud Dialog which contains a list of UPI ids
async function linkMultipleUnassignedPudFromId(payload: {
  pudId: string;
  unassignedPudIds: string[];
}) {
  const pudItem = jobDetails.value.pudItems.find(
    (item) => !!item.pudId && item.pudId === payload.pudId,
  );
  if (!pudItem?.pudId) {
    console.error(
      'Could not add unassigned pud ids to pudItem. pudId: ',
      payload.pudId,
    );
    return;
  }
  // Remove all unassigned pud ids from pud in job
  pudItem.unassignedPudItemReference = [];
  payload.unassignedPudIds.forEach((id) => {
    linkSelectedUnassignedPudItem(pudItem, id);
  });
  closeUnassignedPudDialog();
  // Re-request the route now that we've added new stops
  const updatedRoute = await jobDetails.value.getPlannedRoute();
  updateJobRouteWithIncoming(updatedRoute ?? undefined);
}

// Capture emit from Unassigned Pud Dialog which contains an id of UPI
// Find the matching UnassignedPudItem from unassignedPudListForClient
// Convert to pud item, then set the values for Upi reference and reference to currentPudItem
function linkSelectedUnassignedPudItem(pudItem: PUDItem, id: string) {
  const foundUpi = bookingData.value?.unassignedPudItems?.find(
    (p) => p.id === id,
  );
  if (!foundUpi) {
    return;
  }
  const unassignedPudItem = foundUpi.asPudItem();
  mergePudItemDetailsWithUnassigned(pudItem, unassignedPudItem);

  // We should now add our groupId (client supplied reference on the unassigned
  // pud) as a job reference.
  if (jobDetails.value?.jobReference) {
    addUnassignedPudJobReference(
      foundUpi.groupReferenceId,
      props.jobDetails.jobReference,
    );
  }
}

/**
 * Requests all client data required for booking a job. This includes service
 * rates, fuel surcharge, unassigned puds, client persons etc.
 * @param client - The client for which to request data.
 */
async function requestClientData(client: ClientDetails) {
  const result =
    await clientDetailsStore.requestJobBookingDataForClient(client);

  // If result is null then it means something went wrong
  if (!result) {
    handleMissingData();
    return;
  }
  await setRequiredBookingData(result);
}

/**
 * Called when we have missing data and cannot continue
 */
function handleMissingData() {
  // TODO: This is a placeholder. What do we do in this case?
}

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: 'Job Booking',
  });
}

// TODO: What validation do we have to do here, considering we validate every
// sub-form?
const formIsValid: ComputedRef<boolean> = computed(() => {
  return true;
});

/**
 * Captures emit from save job confirmation button component. Triggers start of
 * save process. Calls either savePermanentJob or confirmJobSave depending on
 * the recurrence type.
 * @param approveJob - Whether the job should be approved or not.
 */
function saveAdhocOrPermanentJob(approveJob: boolean) {
  // Check for missing details in the main job details form, including service
  // type and service rate
  if (!formIsValid.value) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  if (recurrenceType.value === JobRecurrenceType.PERMANENT) {
    savePermanentJob();
  } else {
    confirmJobSave(approveJob);
  }
}

/**
 * Dispatches request to save the current job as a permanent job. Shows a
 * notification on success or failure.
 */
async function savePermanentJob() {
  if (!recurrenceDetails.value?.nextScheduledRecurrence) {
    showAppNotification('Required recurrence information is missing.');
    return;
  }
  const templateFromJobDetails: RecurringJobTemplate =
    new RecurringJobTemplate();
  templateFromJobDetails.fromJobDetails(jobDetails.value);

  awaitingSaveResponse.value = true;
  if (jobDetails.value.pudItems.length > 0) {
    const pud = jobDetails.value.pudItems[0];
    const dateStr = moment(recurrenceDetails.value.nextScheduledRecurrence)
      .tz(useCompanyDetailsStore().userLocale)
      .format('DD/MM/YYYY');
    const dateTime = moment(dateStr + ' ' + pud.pickupTime, 'DD/MM/YYYY HH:mm')
      .tz(useCompanyDetailsStore().userLocale)
      .valueOf();
    recurrenceDetails.value.nextScheduledRecurrence = dateTime;
  }
  templateFromJobDetails.recurrenceDetails = recurrenceDetails.value;
  templateFromJobDetails.directToInvoicing = jobDetails.value.isDirectToInvoice;
  templateFromJobDetails.accounting = jobDetails.value.accounting;
  templateFromJobDetails.notes = jobDetails.value.notes;

  // Send save request
  const savedTemplate = await useRecurringJobStore().saveRecurringJobTemplate(
    templateFromJobDetails,
  );

  // If the response is null, something went wrong in the save. Show a
  // notification and don't reset state.
  if (savedTemplate === null) {
    showAppNotification('Failed to save recurring job template');
    return;
  }

  showAppNotification(
    `Permanent job was successfully saved.`,
    HealthLevel.SUCCESS,
  );

  // Emit to parent to return to hide form view, and display success dialog
  emit('finishedEditing', {
    confirmDiscard: false,
    successDialogConfig: {
      type: JobBookingType.PERMANENT,
      details: savedTemplate,
      clientDetails: selectedClientDetails.value,
    },
  });
}

/**
 * Emitted from the job approval confirmation. args approveJob is equal to the
 * checkbox in the confirmation. Because the checkbox is required to action
 * the confirmation, approveJob should always be true when the job requires
 * review. If the job does not require review the approveJob arg will be false
 * as it doesn't need to be approved. In both cases the job need to be saved.
 * Potential improvements here for less confusion.
 * @param approveJob - Whether the job should be approved or not.
 */
async function confirmJobSave(approveJob: boolean) {
  if (jobDetails.value.pudItems.length > 1 && !jobDetails.value.plannedRoute) {
    showAppNotification('Route information was not found.');
    return;
  }

  // Check for errors in the pud items
  if (
    !pudDataIsValid(
      jobDetails.value,
      selectedClientDetails.value.weightRequirement,
    )
  ) {
    showAppNotification(
      'Some Required data is missing from stops. Please review highlighted Pickups and Deliveries',
    );
    return;
  }

  // If approval is required via ACTION_REQUIRED (45), send the
  // approveJobCompletedActionRequired request.
  if (
    approveJob &&
    jobDetails.value.jobId &&
    jobDetails.value.statusList.includes(45)
  ) {
    awaitingApprovalResponse.value = true;
    const approvalResponse = await jobStore.approveJobCompletedActionRequired(
      jobDetails.value.jobId,
    );
    awaitingApprovalResponse.value = false;
    if (!approvalResponse) {
      showAppNotification(
        'Something went wrong! This job could not be marked as approved. Please try again later.',
      );
      return;
    }
    // On success, set the updated statusList and eventList to the job
    jobDetails.value.statusList = approvalResponse.statusList;
    jobDetails.value.eventList = approvalResponse.eventList;
  }
  if (sessionManager.isClientPortal()) {
    // save Job for clientPortal
    saveClientPortalJob();
  } else {
    saveJob();
  }
}

/**
 * Make sure that minimum required fields are being populated, such as
 * references and job pickup dates
 * @param jobDetails - The jobDetails object to update
 */
function populateRequiredFieldsBeforeSave(jobDetails: JobDetails) {
  // Add default JobReferenceDetails to JobDetails.jobReference, if none
  // currently exist in the list. This prevents null errors on mobile
  if (jobDetails.jobReference.length === 0) {
    jobDetails.jobReference.push(new JobReferenceDetails(4, ''));
  }
  // Refresh the pickupDate in each pudItem to be startOfDay of the respective epochTime
  jobDetails.pudItems.forEach((pud) => {
    const pickupDate: number = returnStartOfDayFromEpoch(pud.epochTime);
    pud.pickupDate = pickupDate;
  });
}

/**
 * Validates form and dispatches request to save the quote. Shows a notification
 * on success or failure.
 */
async function saveQuote(): Promise<void> {
  // Check for missing details in the main job details form, including service
  // type and service rate
  if (!formIsValid.value) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  // Check for errors in the pud items
  if (
    !pudDataIsValid(
      jobDetails.value,
      selectedClientDetails.value.weightRequirement,
    )
  ) {
    showAppNotification(
      'Some Required data is missing from stops. Please review highlighted Pickups and Deliveries',
    );
    return;
  }
  awaitingSaveResponse.value = true;
  const result = await jobBookingStore.saveJobQuote(
    jobDetails.value.client.id,
    jobDetails.value,
  );

  if (result) {
    // Emit to parent to return to hide form view, and display success dialog
    emit('finishedEditing', {
      confirmDiscard: false,
      successDialogConfig: {
        type: JobBookingType.QUOTE,
        details: result,
        clientDetails: selectedClientDetails.value,
      },
    });
  } else {
    showAppNotification(
      `Something went wrong! Quote could not be saved. Please try again later.`,
    );
  }
  awaitingSaveResponse.value = false;
  // emit('finishedEditing', { confirmDiscard: false, jumpToDashboard: false });
}

/**
 * Validates the form and saves the current job details object being edited.
 * On success, returns the user to the dashboard screen.
 */
async function saveClientPortalJob(): Promise<void> {
  try {
    awaitingSaveResponse.value = true;
    // Dispatch save request for the job
    const savedJobId = await clientPortalStore.saveClientJob(jobDetails.value);
    const quoteId = props.jobDetails.additionalJobData?.appliedQuoteId ?? '';
    if (savedJobId && quoteId) {
      await jobBookingStore.acceptQuote(quoteId, savedJobId);
    }
    awaitingSaveResponse.value = false;
    if (savedJobId) {
      // Show success notification
      showNotification(`Job #${savedJobId} saved successfully!`, {
        title: 'Job Booking',
        type: HealthLevel.SUCCESS,
      });
      // Emit to parent to return to hide form view, and display success dialog
      const savedJobDetails = initialiseJobDetails(jobDetails.value);
      savedJobDetails.jobId = savedJobId;
      emit('finishedEditing', {
        confirmDiscard: false,
        successDialogConfig: {
          type: JobBookingType.ADHOC,
          details: savedJobDetails,
          clientDetails: selectedClientDetails.value,
        },
      });
    } else {
      // Show error notification if save failed
      showNotification(
        'Something went wrong - the job could not be saved. Please try again later.',
        {
          title: 'Job Booking',
        },
      );
    }
  } catch (error) {
    // Handle any potential errors during the save request
    console.error('Error saving client job:', error);
    showNotification('An unexpected error occurred. Please try again later.', {
      title: 'Job Booking',
      type: HealthLevel.ERROR,
    });
  }
}

async function saveJob(): Promise<void> {
  // Validate form fields
  if (!formIsValid.value) {
    showAppNotification(FORM_VALIDATION_FAILED_MESSAGE);
    return;
  }

  // Check if COMPLETED ACTION REQUIRED status exists and remove it if necessary. Show notification if status removal fails.
  const removedActionRequiredStatus = removeActionRequiredStatus(
    jobDetails.value,
  );
  if (removedActionRequiredStatus !== true) {
    showAppNotification(removedActionRequiredStatus);
    return;
  }

  // Add fuel surcharge to the job if it doesn't already exist
  const existingReviewedEventExists = jobDetails.value.eventList.some(
    (event) => event.updatedStatus === 'REVIEWED',
  );
  if (!existingReviewedEventExists) {
    jobDetails.value.addClientFuelSurchargeRate(
      bookingData.value?.currentFuelSurcharge ?? null,
    );
  }

  // Set Cash Sale values (if CS type job)
  setCashSaleClientDetails(jobDetails.value);

  // Set isStandbyRate boolean in pudItems and export type on job
  if (selectedClientDetails.value) {
    jobDetails.value.setPudStandbyRates(
      bookingData.value?.commonAddresses ?? [],
    );
  }
  // Ensure required fields are populated/up-to-date on every save
  populateRequiredFieldsBeforeSave(jobDetails.value);

  const isEditingExistingJob = !!jobDetails.value.jobId;
  const isAddingPreloadLeg = hasNewPreloadDropoff(jobDetails.value.pudItems);

  // Dispatch appropriate JobDetails save request
  awaitingSaveResponse.value = true;
  const savedJob = await dispatchSaveRequest(jobDetails.value);
  awaitingSaveResponse.value = false;
  if (!isEditingExistingJob) {
    if (savedJob?.jobId) {
      // If we're adding a new job, emit to parent to return to hide form view,
      // and display success dialog
      emit('finishedEditing', {
        confirmDiscard: false,
        successDialogConfig: {
          type: JobBookingType.ADHOC,
          details: savedJob,
          clientDetails: selectedClientDetails.value,
        },
      });
    }
  } else {
    if (savedJob && isAddingPreloadLeg) {
      // If we're editing a job and it has a preload dropoff, emit to parent to
      // return to hide form view, and display success dialog
      emit('finishedEditing', {
        confirmDiscard: false,
        successDialogConfig: {
          type: JobBookingType.PRELOAD,
          details: savedJob,
          clientDetails: selectedClientDetails.value,
        },
      });
    } else {
      // If editing job, reset component and return to dashboard
      emit('finishedEditing', {
        confirmDiscard: false,
        jumpToDashboard: true,
      });
    }
  }
}
/**
 * Dispatch appropriate save request. Types:
 * 1. Update Completed Job to In Progress
 * 2. Regular Save
 * 3. Save Job and Update UnassignedPudItem
 *
 * If we're saving a new job, we will return the saved JobDetails so we can
 * display the success dialog.;
 */
async function dispatchSaveRequest(
  jobDetails: JobDetails,
): Promise<JobDetails | null> {
  let notificationMessage = '';
  let notificationType = HealthLevel.ERROR;
  let displayId = jobDetails.displayId;

  // check if appliedQuoteId has value
  const quoteId = props.jobDetails.additionalJobData?.appliedQuoteId ?? '';
  // Step 1.
  // - If the job is completed we need to check if the user added another pud
  //   item. If they did we require the job to go back into a in progress state.
  //   If not we can continue and save the job without updating its status.
  const containsUnfinishedPud = jobDetails.pudItems.some(
    (pud: PUDItem) => !pud.status,
  );
  if (
    jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED &&
    containsUnfinishedPud
  ) {
    // First we update the job status back to IN PROGRESS
    const updateJobStatusResult = await jobStore.updateJobStatus(
      jobDetails.jobId,
      JobEventType.StartedJob,
    );
    if (!updateJobStatusResult) {
      notificationMessage = `Something went wrong! Job #${jobDetails.displayId} could not be returned In Progress. Please try again later.`;
    } else {
      // After updating the job to IN PROGRESS, send request to update the job
      const saveResult = await jobStore.updateJobDetails(jobDetails);

      // Show notification based on response
      if (saveResult !== null) {
        notificationMessage = `Job #${displayId} was successfully updated and returned to In Progress.`;
        notificationType = HealthLevel.SUCCESS;
      } else {
        notificationMessage = `Something went wrong! Job #${displayId} could not be saved. Please try again later.`;
      }
    }
    showAppNotification(notificationMessage, notificationType);
    return null;
  }

  let savedJob: JobDetails | null = null;
  // Find new and removed unassigned pudIds by comparing the unassigned puds
  // at the current time, with those at the time the job was loaded
  const updatedUnassignedPudIds = returnAddedOrRemovedUnassignedPudIds(
    jobDetails.pudItems,
    currentUnassignedPudIds.value,
  );

  // If there are no new or removed unassignedPudIds, save job as usual
  if (
    updatedUnassignedPudIds.added.length === 0 &&
    updatedUnassignedPudIds.removed.length === 0
  ) {
    // We have two separate apis for saving a job. One is for a new job and one is for updating an existing job.
    // Check if job is new
    if (!jobDetails._id) {
      const saveResult: JobEventSummary | null =
        await jobStore.saveJobDetails(jobDetails);

      // If we're booking for a quote, and the job save was successful, then we
      // need to mark the quote as accepted
      if (saveResult?.jobId) {
        if (quoteId) {
          await jobBookingStore.acceptQuote(quoteId, saveResult.jobId);
        }
      } else {
        notificationMessage = `Something went wrong! Job #${displayId} could not be saved. Please try again later.`;
      }
      savedJob = saveResult?.jobDetails ?? null;
    } else {
      // job is not new and we are updating an existing job.
      const saveResult = await jobStore.updateJobDetails(jobDetails);
      // Show notification based on response
      if (saveResult !== null) {
        notificationMessage = `Job #${displayId} was successfully updated.`;
        notificationType = HealthLevel.SUCCESS;
      } else {
        notificationMessage = `Something went wrong! Job #${displayId} could not be saved. Please try again later.`;
      }
      savedJob = saveResult?.jobDetails ?? null;
    }
  } else {
    const isBookingNewJob = !jobDetails._id;
    // If there is AT LEAST 1 new OR removed unassigned pudIds, We should
    // dispatch request which updates both. Display error message if request
    // failed
    const saveResult = await dataImportStore.saveUnassignedPudJobDetails(
      jobDetails,
      updatedUnassignedPudIds.added,
      updatedUnassignedPudIds.removed,
    );

    // If we're booking a new job, booking from a quote from a quote, and the
    // job save was successful, then we need to mark the quote as accepted
    if (isBookingNewJob && quoteId && saveResult?.jobDetails.jobId) {
      await jobBookingStore.acceptQuote(quoteId, saveResult.jobDetails.jobId);
    }

    // Show notification based on response
    if (saveResult !== null) {
      notificationMessage = `Job #${displayId} was successfully updated.`;
      notificationType = HealthLevel.SUCCESS;
    } else {
      notificationMessage = `Something went wrong! Job #${displayId} could not be saved. Please try again later.`;
    }
    savedJob = saveResult?.jobDetails ?? null;
  }
  if (notificationMessage) {
    showAppNotification(notificationMessage, notificationType);
  }
  return savedJob;
}

// Listener method for leg linking responses. We look at the updates in the
// response and update the job to keep things in sync.
function setIncomingLegLinkingResponse(
  response: UnassignedPudJobDetailsResponse | null,
) {
  if (
    awaitingSaveResponse.value ||
    !response?.jobDetails ||
    response.jobDetails.jobId !== jobDetails.value.jobId
  ) {
    return;
  }
  // Get the last event and user who updated the job, so we can display a notification
  const lastEvent =
    response.jobDetails.eventList[response.jobDetails.eventList.length - 1];
  const editedBy =
    lastEvent?.updatedStatus === 'JOB_DETAILS_UPDATED'
      ? `by ${lastEvent.editedBy}`
      : '';
  // Display notification to user and return to new booking page
  showAppNotification(
    `This job was recently updated (Leg Matching) ${editedBy}. No updates are allowed at this time.`,
    HealthLevel.WARNING,
  );
  emit('finishedEditing', {
    confirmDiscard: false,
  });
}

/**
 * Mitt callback for jobStatusUpdate, updatedStatusJobDetails and
 * updateJobEventListResponse events. Used to update the currently editing job
 * such that it's kept in sync with state changes.
 * @param payload JobEventSummary payload
 */
function handleJobStatusUpdate(payload: JobEventSummary | null) {
  if (
    !payload ||
    payload.jobId !== jobDetails.value.jobId ||
    (awaitingSaveResponse.value && payload.jobId === jobDetails.value.jobId)
  ) {
    return;
  }
  if (
    payload.event === 'REVIEWED' ||
    payload.event === 'CancelledJob' ||
    payload.workStatus > WorkStatus.REVIEWED
  ) {
    showAppNotification(
      'This job was recently reviewed or cancelled. No updates are allowed at this time.',
    );
    emit('finishedEditing', { confirmDiscard: false });
    return;
  }
  if (!payload.jobDetails && payload.statusList && payload.latestEvent) {
    jobDetails.value.statusList = payload.statusList;
    jobDetails.value.workStatus = payload.workStatus;
    jobDetails.value.eventList.push(payload.latestEvent);
  }
  if (payload.event === 'UpdateJobDetails') {
    if (payload.jobDetails) {
      showAppNotification(
        'This job was recently changed. To minimise conflicts, any pending changes were reset.',
      );
      emit('finishedEditing', { confirmDiscard: false });
    }
    return;
  }
  if (payload.jobDetails) {
    jobDetails.value.statusList = payload.jobDetails.statusList;
    jobDetails.value.eventList = payload.jobDetails.eventList;
    jobDetails.value.fleetAssetId = payload.jobDetails.fleetAssetId;
    jobDetails.value.driverId = payload.jobDetails.driverId;
    if (payload.event === 'DeallocateJob') {
      jobDetails.value.accounting.fleetAssetRates = [];
    } else if (payload.event === 'PreAllocateJob') {
      jobDetails.value.accounting.fleetAssetRates =
        payload.jobDetails.accounting.fleetAssetRates;
    }
    jobDetails.value.workStatus = payload.jobDetails.workStatus;
  }
}

/**
 * Mitt callback for pudStatusUpdate event. Used to update the currently
 * editing pud item such that it's kept in sync with state changes.
 * @param payload pudStatusUpdate payload
 */
function handlePudStatusUpdate(payload: PudStatusUpdate | null) {
  if (!!payload && payload.jobId === jobDetails.value.jobId) {
    const updateTo = payload.pudStatus || null;
    // Updated editedPudItem in child component if the update is for that pud
    if (jobBookingAddStopDialog.value?.editedPudItem?.pudId === payload.pudId) {
      jobBookingAddStopDialog.value.editedPudItem.status = updateTo;
    }
    // Update the pudItem in the jobDetails
    const pudItem = jobDetails.value.pudItems.find(
      (pud) => pud.pudId === payload.pudId,
    );
    if (pudItem) {
      pudItem.status = updateTo;
    }
  }
}

/**
 * Mitt callback to handle division-level message for attachment update
 * @param payload containing update details
 */
function handleAttachmentUpdate(payload: AddAttachmentToJob | null) {
  if (
    jobDetails.value.jobId &&
    payload?.jobId === jobDetails.value.jobId &&
    !!payload.pudId
  ) {
    const updateAttachments = (pudItem: PUDItem) => {
      const attIdx = pudItem.attachments.findIndex(
        (pa) => pa.id === payload.attachment.id,
      );
      if (attIdx !== -1) {
        pudItem.attachments[attIdx] = payload.attachment;
      } else {
        pudItem.attachments.push(payload.attachment);
      }
    };

    if (jobBookingAddStopDialog.value?.editedPudItem?.pudId === payload.pudId) {
      // Update editedPudItem in child component if the update is for that pud
      const editedPudItem: PUDItem =
        jobBookingAddStopDialog.value?.editedPudItem;
      updateAttachments(editedPudItem);
    }

    // Update the pudItem in the jobDetails
    const pudItemInJob = jobDetails.value.pudItems.find(
      (pud) => pud.pudId === payload.pudId,
    );
    if (pudItemInJob) {
      updateAttachments(pudItemInJob);
    }
  }
}

/**
 * Handles division-level response for updating a client's operational status.
 * Updates the client's operational status in the store.
 * @param clientOperationalStatusUpdate contains details of status update
 */
function handleClientOpStatusUpdate(
  clientOperationalStatusUpdate: ClientOperationalStatusUpdate | null,
): void {
  if (!clientOperationalStatusUpdate) {
    return;
  }
  if (
    !jobDetails.value.jobId &&
    jobDetails.value.client.id === clientOperationalStatusUpdate.clientId
  ) {
    clientClosedDialogIsActive.value = true;
  }
}

/**
 * Mitt callback for when a note is added to a job. Adds it to the currently
 * editing job to keep it in sync
 * @param payload AddNoteToJobRequest containing the jobId, pudId and the
 * Communication object to add
 */
function handleNoteUpdate(payload: AddNoteToJobRequest | null): void {
  if (
    !payload?.note ||
    !jobDetails.value.jobId ||
    jobDetails.value.jobId !== payload.jobId
  ) {
    return;
  }
  const newNote: Communication = Object.assign(
    new Communication(),
    payload.note,
  );

  const updateNotes = (notes: Communication[]) => {
    notes ??= [];
    const foundNoteIdx = notes.findIndex((note) => note.id === newNote.id);
    if (foundNoteIdx !== -1) {
      notes[foundNoteIdx] = newNote;
    } else {
      notes.push(newNote);
    }
  };

  // If pudId is null, then push into job notes
  if (!payload.pudId) {
    updateNotes(jobDetails.value.notes);
  } else {
    // If pudId is provided, find the pudItem and push into its notes
    // Update editedPudItem in child component if the update is for that pud
    if (jobBookingAddStopDialog.value?.editedPudItem?.pudId === payload.pudId) {
      const editedPudItem: PUDItem =
        jobBookingAddStopDialog.value.editedPudItem;
      updateNotes(editedPudItem.notes);
    }

    // Update pudItem in jobDetails
    const foundPud = jobDetails.value.pudItems.find(
      (pud) => pud.pudId === payload.pudId,
    );
    if (foundPud) {
      updateNotes(foundPud.notes);
    }
  }
}

function cancel() {
  emit('finishedEditing', { confirmDiscard: true });
}

// function to delete job note
function handleRemoveNote(index: number) {
  if (jobDetails.value) {
    jobDetails.value.notes.splice(index, 1);
  }
}

onMounted(() => {
  setInitialData();
});

useMittListener('clientOperationalStatusUpdate', handleClientOpStatusUpdate);
useMittListener('addedNoteToJob', handleNoteUpdate);
useMittListener('updatedJobWithAttachment', handleAttachmentUpdate);
useMittListener('pudStatusUpdate', handlePudStatusUpdate);
useMittListener('jobStatusUpdate', handleJobStatusUpdate);
useMittListener('updatedStatusJobDetails', handleJobStatusUpdate);
useMittListener('updateJobEventListResponse', handleJobStatusUpdate);
useMittListener(
  'savedJobDetailsAndUpdatedUnassignedPudItems',
  setIncomingLegLinkingResponse,
);

// func to scroll to pud item
function scrollTo(id: string | undefined) {
  highlight.value = id ?? '';
  jobBookingDeliverySummary.value.scrollToPudItem(id);

  setTimeout(() => {
    highlight.value = '';
  }, 2000);
}

function scrollToTop(): void {
  const el = document.getElementById('form');
  if (el) {
    nextTick(() =>
      el.scrollTo({
        top: 0,
        behavior: 'smooth',
      }),
    );
  }
  setTimeout(() => {
    highlight.value = '';
  }, 2000);
}
</script>

<style scoped lang="scss">
@keyframes slide {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0%);
  }
}

@-webkit-keyframes slide {
  0% {
    -webkit-transform: translateY(0%);
  }
  100% {
    -webkit-transform: translateY(-100%);
  }
}

.success-dialog {
  z-index: 999;
}

.job-booking-form {
  display: flex;
  flex-direction: column;
  position: relative;
  // font-family: $sub-font-family;

  .form-section {
    padding: 8px 16px;
    gap: 8px;
    .form-card {
      background-color: var(--background-color-card) !important;
      border: 2px solid transparent;
      transition: all 1s ease;
      &.highlight {
        border: 2px solid var(--primary);
        transition: color 1s ease;
      }
      .form-card-status-indicator {
        min-width: 22px;
        min-height: 22px;
        max-width: 22px;
        max-height: 22px;
        margin-left: 10px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: $font-size-12;
        color: white;
        background-color: var(--success-secondary);
      }
      &.client-portal {
        .scrolled {
          width: 86.7%;
          left: 14.4%;
          top: 124px;
        }
        .pud-header-scrolled {
          width: 86.7%;
          left: 14.4%;
          top: 222px;
          border-top: 1px solid var(--border-color);
        }
      }
      margin-top: 4px;
      &.second {
        margin: 0;
      }
      padding: 8px 16px;
      border-radius: 8px;
      box-shadow: $box-shadow-lg;
      border-top: 0px;
      border: 1px solid var(--background-color-500);
      .form-title-txt {
        color: var(--text-color);
        font-size: $font-size-20;
        font-weight: 600;
        font-family: $sub-font-family;
      }
      .delivery-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 14px;

        .pud-total-txt {
          color: var(--light-text-color);
          font-weight: 500;
          margin-top: 2px;
          .pud-count {
            background-color: var(--bg-light-blue);
            height: 22px;
            width: auto;
            padding: 0px 5px 0 5px;
            margin-left: 3px;
            margin-right: 16px;
            border-radius: 10px;
            font-weight: 700;
            color: var(--background-color-400);
          }
        }
      }
      .scrolled {
        position: fixed;
        padding: 12px 18px 12px 28px !important;
        border-radius: 0px;
        top: 76px;
        scale: 0.9;
        max-height: 110px;
        min-height: 110px;
        z-index: 10;
        left: 12%;
        width: 66.6%;
        border: none;
        border-left: 2px solid var(--border-color);
        background: var(--overlay-background);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        animation: slide 0.3s forwards;
        -webkit-animation: slide 0.3s forwards;
      }

      .pud-header-scrolled {
        padding: 12px 18px 4px 28px !important;
        // padding: 6px 8px 6px 8px !important;
        border-radius: 0 0 0 8px;
        background: var(--overlay-background);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        position: fixed;
        scale: 0.9;
        z-index: 10;
        left: 12%;
        width: 66.6%;
        opacity: 1 !important;
        top: 166px;
        border-left: 2px solid var(--border-color);
        display: flex;
        flex-direction: row-reverse;
        // justify-content: center;
        border-bottom: 2px solid var(--border-color);
        border-top: 2px solid $translucent;
        animation: slide 0.4s forwards;
        -webkit-animation: slide 0.4s forwards;

        .delivery-header {
          align-self: flex-end;
          > :not(.btn-container) {
            display: none;
          }
        }
        .form-title-txt {
          font-size: $font-size-18;
          font-weight: 700;
        }
        .pud-item-flag-text {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .scrolling {
        margin-top: 100px;
      }
      &.action-btns {
        padding: 2px;
      }
    }
    .notes-header {
      border-bottom: 2px dotted var(--yellow);
    }
    .notes-list-container {
      max-height: 210px;
    }
  }

  .edit-icons-btn {
    background-color: $info;
    border-radius: 20px;
    color: white !important;
  }

  .btn-container {
    display: flex;
    flex-direction: row;
  }

  .quick-add-btn {
    color: var(--accent-secondary) !important;
    padding: 14px;
    font-weight: 600;
    border: 2px solid var(--accent-secondary);
    border-radius: 14px;
  }

  .add-icons-btn {
    background-color: var(--primary-light);
    border-radius: 20px;
    color: var(--background-color-400) !important;
  }

  .addleg-icon-btn {
    border-radius: 20px;
    background-color: var(--primary-light) !important;
    color: var(--background-color-400) !important;
  }

  .save-btn {
    color: $success;
    border-color: $success;
    .v-btn {
      margin-right: 4px;
      color: $success;
    }
  }

  .v-btn-custom {
    font-family: $sub-font-family;
    margin: 2px 18px;
    border-radius: $border-radius-btn;
    font-weight: 800;
    background-color: transparent !important;
    border: 1px solid $border-color;
    background: none;
    transition:
      transform 0.5s ease-in-out,
      background-color 0.3s ease;
    &.pickup {
      border: 2px solid var(--border-color);
      color: $pickup;
      &:hover {
        background-color: $pickup-highlight !important;
        background: $pickup-color;
      }
    }
    &.drop {
      border: 2px solid var(--border-color);
      color: $drop;
      &:hover {
        background-color: $drop-highlight !important;
        background: $drop-color;
      }
    }
    &:hover {
      transform: scale(1.05);
      box-shadow: $box-shadow !important;
      color: white;
    }
  }
}
.icon-btn-container {
  display: flex;
  flex-direction: row;
  justify-content: end;
  gap: 4px;
  transition: all 0.3s ease;
  .icon-action-btn {
    bottom: 60px;
    display: inline;
    border-radius: 50px;
    transition: all 0.3s ease;
    &.scroll-up {
      margin-left: 22px;
      width: 36px;
      height: 36px;
      border: 1px solid var(--text-color);
      background: none;
      bottom: 60px;
      display: inline;
      .v-icon {
        color: var(--text-color);
        padding-right: 2px;
      }
      &:hover {
        width: 36px;
        background-color: $translucent-light;
      }
    }
  }
}

// Responsive Styles
@media only screen and (min-device-width: 1025px) and (max-device-width: 1440px) {
  //your styles here
  .job-booking-form {
    margin-top: 10px;
    font-size: smaller !important;
  }
  .form-title-txt {
    font-size: $font-size-17 !important;
  }

  .scrolled {
    width: 66% !important;
    left: 13% !important;
  }
  .pud-header-scrolled {
    width: 66% !important;
    left: 13% !important;
  }

  .edit-icons-btn {
    height: 28px;
    width: 28px;
  }

  .add-icons-btn {
    height: 28px;
    width: 28px;
  }

  .addleg-icon-btn {
    height: 28px;
    width: 28px;
  }
}

@media (max-width: 768px) {
  .job-booking-form {
    margin-top: 10px;
    font-size: smaller !important;
  }
  .form-title-txt {
    font-size: small !important;
  }

  .scrolled {
    width: 64% !important;
    left: 15% !important;
  }
  .pud-header-scrolled {
    width: 64% !important;
    left: 15% !important;
  }
}

// ipad air
@media only screen and (min-device-width: 1024px) and (max-device-width: 1180px) {
  .job-booking-form {
    margin-top: 10px;
    font-size: smaller !important;
  }

  .form-title-txt {
    font-size: small !important;
  }

  .scrolled {
    width: 65% !important;
    left: 14% !important;
  }
  .pud-header-scrolled {
    width: 65% !important;
    left: 14% !important;
  }
}
</style>

import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import QuickSelectDateRange from '@/components/common/date-picker/quick_select_date_range.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import FormCard from '@/components/common/ui-elements/form_card.vue';
import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
@Component({
  components: {
    FormCard,
    QuickSelectDateRange,
    DateTimeInputs,
    ContentDialog,
  },
})
export default class FuelSurchargeLevy extends Vue implements IUserAuthority {
  @Prop() public fuelSurchargeRate:
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate;
  @Prop() public allFuelSurchargeRates:
    | ClientFuelSurchargeRate[]
    | FleetAssetFuelSurchargeRate[];
  @Prop({ default: false }) public isDivision: boolean;
  @Prop({ default: false }) public isCashSale: boolean;
  @Prop({ default: false }) public isFleetAsset: boolean;
  @Prop({ default: false }) public isClient: boolean;
  @Prop() public clientName: string;
  @Prop({ default: true }) public formActive: boolean;
  @Prop({ default: false }) public isExpiredFuelSurcharge: boolean;
  @Prop({ default: null }) public activeFuelSurchargeTableId: number | null;
  @Prop({}) public entityId: string;
  @Prop() public activeFuelSurchargeRate:
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate
    | null;

  public companyDetailsStore = useCompanyDetailsStore();
  public fuelLevyStore = useFuelLevyStore();

  public invalidManualEnter: boolean = false;
  public returnFormattedDate = returnFormattedDate;
  public isAwaitingSaveResponse: boolean = false;

  public isNewFuelSurcharge: boolean = false;
  public isEdited: boolean = false;
  public editedFuelSurchargeRate:
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate
    | null = null;

  public tableHeaders: TableHeader[] = [
    {
      text: 'Rate',
      align: 'left',
      value: 'result',
      sortable: false,
    },
    {
      text: 'Valid From',
      align: 'left',
      value: 'result',
      sortable: false,
    },
    {
      text: 'Valid To',
      align: 'left',
      sortable: false,
      value: '',
    },
    {
      text: 'Active',
      align: 'left',
      sortable: false,
      value: '',
    },
  ];

  // Controls dialog visibility, and resets local working variables on close
  get dialogController() {
    return this.isEdited || this.isNewFuelSurcharge;
  }
  set dialogController(value: boolean) {
    this.isEdited = value;
    this.isNewFuelSurcharge = value;
  }

  public $refs!: {
    fuelSurchargeForm: VForm;
  };

  @Watch('fuelSurchargeRate')
  public rateChanged() {
    if (this.isNewFuelSurcharge) {
      let latestValidToDate: number = 0;
      let ratePercent: number = 0.0;
      for (const rate of this.allFuelSurchargeRates) {
        if (rate.validToDate && rate.validToDate > latestValidToDate) {
          latestValidToDate = rate.validToDate;
          ratePercent = rate.fuelSurchargeRate;
        }
      }
      if (latestValidToDate !== 0) {
        this.fuelSurchargeRate.validFromDate = moment(latestValidToDate)
          .tz(this.companyDetailsStore.userLocale)
          .add(1, 'day')
          .startOf('day')
          .valueOf();
      } else {
        this.fuelSurchargeRate.validFromDate = moment
          .tz(this.companyDetailsStore.userLocale)
          .startOf('day')
          .valueOf();
      }
      this.fuelSurchargeRate.fuelSurchargeRate = ratePercent;
    }
  }

  get validate(): Validation {
    return validationRules;
  }

  get allowedDates() {
    const min = moment
      .tz(this.companyDetailsStore.userLocale)
      .subtract(5, 'year');
    let dates: any = {
      from: {
        min: min.valueOf(),
        max:
          !this.fuelSurchargeRate || !this.fuelSurchargeRate.validToDate
            ? undefined
            : moment(this.fuelSurchargeRate.validToDate)
                .tz(this.companyDetailsStore.userLocale)
                .valueOf(),
      },
      to: {
        min:
          !this.fuelSurchargeRate ||
          this.fuelSurchargeRate.validFromDate === null
            ? undefined
            : moment(this.fuelSurchargeRate.validFromDate)
                .tz(this.companyDetailsStore.userLocale)
                .valueOf(),
        max: undefined,
      },
    };

    if (this.allFuelSurchargeRates.length === 0) {
      return dates;
    }

    if (!this.isEdited) {
      dates = {
        from: {
          min: this.latestFuelSurcharge
            ? moment(this.latestFuelSurcharge.validFromDate)
                .tz(this.companyDetailsStore.userLocale)
                .add(1, 'day')
                .valueOf()
            : undefined,
          max: !this.fuelSurchargeRate.validToDate
            ? undefined
            : moment(this.fuelSurchargeRate.validToDate + 1)
                .tz(this.companyDetailsStore.userLocale)
                .subtract(1, 'day')
                .valueOf(),
        },
        to: {
          min:
            this.fuelSurchargeRate.validFromDate === null
              ? undefined
              : moment(this.fuelSurchargeRate.validFromDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .valueOf(),
          max: undefined,
        },
      };
      return dates;
    }

    if (this.allFuelSurchargeRates.length > 1) {
      if (
        this.latestFuelSurcharge &&
        this.fuelSurchargeRate.id === this.latestFuelSurcharge.id
      ) {
        dates = {
          from: {
            min: this.previousFuelSurcharge
              ? moment(this.previousFuelSurcharge.validFromDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .add(1, 'day')
                  .valueOf()
              : undefined,
            max: !this.fuelSurchargeRate.validToDate
              ? undefined
              : moment(this.fuelSurchargeRate.validToDate)
                  .tz(this.companyDetailsStore.userLocale)

                  .valueOf(),
          },
          to: {
            min:
              this.fuelSurchargeRate.validFromDate === null
                ? undefined
                : moment(this.fuelSurchargeRate.validFromDate)
                    .tz(this.companyDetailsStore.userLocale)
                    .valueOf(),
            max: undefined,
          },
        };
      } else if (
        this.earliestFuelSurcharge &&
        this.fuelSurchargeRate.id === this.earliestFuelSurcharge.id
      ) {
        dates = {
          from: {
            min: min.valueOf(),
            max: !this.fuelSurchargeRate.validToDate
              ? undefined
              : moment(this.fuelSurchargeRate.validToDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .subtract(1, 'day')
                  .valueOf(),
          },
          to: {
            min:
              this.fuelSurchargeRate.validFromDate === null
                ? undefined
                : moment(this.fuelSurchargeRate.validFromDate)
                    .tz(this.companyDetailsStore.userLocale)
                    .format('YYYY-MM-DD'),
            max: this.nextFuelSurcharge
              ? moment(this.nextFuelSurcharge.validToDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .subtract(1, 'day')
                  .valueOf()
              : undefined,
          },
        };
      } else {
        dates = {
          from: {
            min: this.previousFuelSurcharge
              ? moment(this.previousFuelSurcharge.validFromDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .add(1, 'day')
                  .valueOf()
              : undefined,
            max: !this.fuelSurchargeRate.validToDate
              ? this.nextFuelSurcharge
                ? moment(this.nextFuelSurcharge.validFromDate)
                    .tz(this.companyDetailsStore.userLocale)
                    .subtract(1, 'day')
                    .valueOf()
                : undefined
              : moment(this.fuelSurchargeRate.validToDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .valueOf(),
          },
          to: {
            min: !this.fuelSurchargeRate.validFromDate
              ? undefined
              : moment(this.fuelSurchargeRate.validFromDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .valueOf(),
            max: this.nextFuelSurcharge
              ? moment(this.nextFuelSurcharge.validToDate)
                  .tz(this.companyDetailsStore.userLocale)
                  .subtract(1, 'day')
                  .valueOf()
              : undefined,
          },
        };
      }
    }
    return dates;
  }

  get disabledQuickSelect(): boolean {
    if (this.allFuelSurchargeRates.length <= 1) {
      return false;
    }
    if (this.isNewFuelSurcharge) {
      return false;
    }

    if (
      this.latestFuelSurcharge !== undefined &&
      this.fuelSurchargeRate.id === this.latestFuelSurcharge.id
    ) {
      return false;
    } else {
      return true;
    }
  }

  get previousFuelSurcharge():
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate
    | undefined {
    const foundEditedFuelSurcharge = this.allFuelSurchargeRates.find(
      (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
        charge.id === this.fuelSurchargeRate.id,
    );
    if (!foundEditedFuelSurcharge) {
      return this.latestFuelSurcharge;
    }
    return this.allFuelSurchargeRates.find(
      (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
        charge.validToDate! < foundEditedFuelSurcharge.validFromDate! &&
        charge.validToDate! >
          foundEditedFuelSurcharge.validFromDate! - 80400000,
    );
  }

  get nextFuelSurcharge():
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate
    | undefined {
    const foundEditedFuelSurcharge = this.allFuelSurchargeRates.find(
      (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
        charge.id === this.fuelSurchargeRate.id,
    );
    if (foundEditedFuelSurcharge) {
      return this.allFuelSurchargeRates.find(
        (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
          charge.validFromDate! > foundEditedFuelSurcharge.validToDate! &&
          charge.validFromDate! <
            foundEditedFuelSurcharge.validToDate! + 80400000,
      );
    }
  }

  get validToEdits() {
    let foundEditedFuelSurcharge = this.allFuelSurchargeRates.find(
      (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
        charge.id === this.fuelSurchargeRate.id,
    );

    if (!this.isEdited) {
      foundEditedFuelSurcharge = this.fuelSurchargeRate;
    }
    const requiredEdits: any = {
      edit: false,
      surchargeId: '',
      validFromValue: 0,
    };
    if (
      this.fuelSurchargeRate.validToDate &&
      this.nextFuelSurcharge &&
      this.nextFuelSurcharge.validFromDate &&
      foundEditedFuelSurcharge &&
      foundEditedFuelSurcharge.validToDate
    ) {
      if (
        this.fuelSurchargeRate.validToDate >=
          this.nextFuelSurcharge.validFromDate ||
        this.fuelSurchargeRate.validToDate <
          foundEditedFuelSurcharge.validToDate
      ) {
        requiredEdits.edit = true;
        requiredEdits.surchargeId = this.nextFuelSurcharge.id;

        requiredEdits.validFromValue = moment(
          this.fuelSurchargeRate.validToDate,
        )
          .tz(this.companyDetailsStore.userLocale)
          .add(1, 'day')
          .startOf('day')
          .valueOf();
      }
    }
    return requiredEdits;
  }

  get validFromEdits() {
    let foundEditedFuelSurcharge = this.allFuelSurchargeRates.find(
      (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
        charge.id === this.fuelSurchargeRate.id,
    );

    if (!this.isEdited) {
      foundEditedFuelSurcharge = this.fuelSurchargeRate;
    }
    const requiredEdits: any = {
      edit: false,
      surchargeId: '',
      validToValue: 0,
    };
    if (
      this.fuelSurchargeRate.validFromDate &&
      this.previousFuelSurcharge &&
      this.previousFuelSurcharge.validToDate &&
      foundEditedFuelSurcharge &&
      foundEditedFuelSurcharge.validFromDate
    ) {
      if (
        this.fuelSurchargeRate.validFromDate <=
          this.previousFuelSurcharge.validToDate ||
        this.fuelSurchargeRate.validFromDate >
          foundEditedFuelSurcharge.validFromDate
      ) {
        requiredEdits.edit = true;
        requiredEdits.surchargeId = this.previousFuelSurcharge.id;

        requiredEdits.validToValue = moment(
          this.fuelSurchargeRate.validFromDate,
        )
          .tz(this.companyDetailsStore.userLocale)
          .subtract(1, 'day')
          .endOf('day')
          .valueOf();
      }
    }
    return requiredEdits;
  }

  get earliestFuelSurcharge():
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate
    | undefined {
    if (
      this.latestFuelSurcharge !== undefined &&
      this.latestFuelSurcharge.validFromDate !== null
    ) {
      let date: number = this.latestFuelSurcharge.validFromDate;
      for (const surcharge of this.allFuelSurchargeRates) {
        if (
          surcharge.validFromDate !== null &&
          surcharge.validToDate !== null
        ) {
          if (surcharge.validFromDate < date) {
            date = surcharge.validFromDate;
          }
        }
      }
      return this.allFuelSurchargeRates.find(
        (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
          charge.validFromDate === date,
      );
    }
  }

  get latestFuelSurcharge():
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate
    | undefined {
    let date: number = 0;
    for (const surcharge of this.allFuelSurchargeRates) {
      if (surcharge.validFromDate === null || surcharge.validToDate === null) {
        continue;
      }
      if (surcharge.validToDate > date) {
        date = surcharge.validToDate;
      }
    }
    return this.allFuelSurchargeRates.find(
      (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
        charge.validToDate === date,
    );
  }

  public returnDateActive(
    fromDate: number,
    toDate: number,
    tableId: number,
  ): boolean {
    if (tableId === this.activeFuelSurchargeTableId) {
      return true;
    }
    const currentTime = moment().valueOf();

    if (currentTime >= fromDate && currentTime <= toDate) {
      return true;
    } else {
      return false;
    }
  }
  // Returns current validFromDate from fuelSurchargeRate. Used as v-model for
  // DateTimeInput. Perform some additional logic when setting.
  get validFromDate(): number | null {
    return this.fuelSurchargeRate.validFromDate;
  }
  set validFromDate(epoch: number | null) {
    if (epoch === null) {
      this.fuelSurchargeRate.validFromDate = null;
      return;
    }
    // Epoch is returned from DateTimeInputs as start of day due to
    // DATE_START_OF_DAY prop on DateTimeInputs
    const startOfDay = epoch;
    this.fuelSurchargeRate.validFromDate = startOfDay;
    // Validate against allowedDates
    if (this.allowedDates.from.max === undefined) {
      this.fuelSurchargeRate.validFromDate = startOfDay;
    } else {
      const allowedDateInEpoch = returnEndOfDayFromEpoch(
        this.allowedDates.from.max,
      );
      // If the selected validFromDate is higher than the allowed maximum value,
      // set value to null and show warning
      if (startOfDay > allowedDateInEpoch) {
        this.fuelSurchargeRate.validFromDate = null;
        this.showWarning();
      } else {
        this.fuelSurchargeRate.validFromDate = startOfDay;
      }
    }
    if (
      this.allowedDates.from.min === undefined &&
      this.fuelSurchargeRate.validFromDate !== null
    ) {
      this.fuelSurchargeRate.validFromDate = startOfDay;
    } else if (this.fuelSurchargeRate.validFromDate) {
      const allowedDateInEpoch = returnStartOfDayFromEpoch(
        this.allowedDates.from.min,
      );
      if (startOfDay < allowedDateInEpoch) {
        this.fuelSurchargeRate.validFromDate = null;
        this.showWarning();
      } else {
        this.fuelSurchargeRate.validFromDate = startOfDay;
      }
    }
  }
  // Returns current validFromDate from fuelSurchargeRate. Used as v-model for
  // DateTimeInput. Perform some additional logic when setting.
  get validToDate(): number | null {
    return this.fuelSurchargeRate.validToDate;
  }
  set validToDate(epoch: number | null) {
    if (epoch === null) {
      return;
    }
    this.fuelSurchargeRate.validToDate = epoch;
    // Epoch is returned from DateTimeInputs as end of day due to
    // DATE_END_OF_DAY prop on DateTimeInputs
    const endOfDay = epoch;
    // If the selected validToDate is higher than the allowed maximum value,
    // set value to null and show warning
    if (
      (this.allowedDates.to.max !== undefined &&
        endOfDay > returnEndOfDayFromEpoch(this.allowedDates.to.max)) ||
      (this.fuelSurchargeRate.validToDate &&
        endOfDay < returnStartOfDayFromEpoch(this.allowedDates.to.min))
    ) {
      this.showWarning();
    }
  }

  public showWarning() {
    this.invalidManualEnter = true;
    setTimeout(() => {
      this.invalidManualEnter = false;
    }, 3000);
  }

  public setQuickValidToDate(epoch: number): void {
    this.fuelSurchargeRate.validToDate = epoch;
  }

  public newFuelSurcharge(): void {
    this.isNewFuelSurcharge = true;
    this.$emit('setNewFuelSurchargeRate');
  }

  public cancelCreateOrEditFuelSurcharge(): void {
    this.isNewFuelSurcharge = false;
    this.isEdited = false;
    this.$emit('setSelectedFuelSurchargeRate', this.editedFuelSurchargeRate);
  }

  get allFuelSurchargeRatesReversed() {
    return [...this.allFuelSurchargeRates].reverse();
  }

  public saveFuelSurcharge(): void {
    if (!this.$refs.fuelSurchargeForm.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }
    this.isAwaitingSaveResponse = true;
    // Update validTo and validFrom dates for edited fuel surcharge rates
    if (this.validFromEdits.edit) {
      const foundFuelSurcharge = this.allFuelSurchargeRates.find(
        (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
          charge.id === this.validFromEdits.surchargeId,
      );
      if (foundFuelSurcharge) {
        foundFuelSurcharge.validToDate = this.validFromEdits.validToValue;
        this.saveValidFromToEdits(foundFuelSurcharge);
      }
    }
    if (this.validToEdits.edit) {
      const foundFuelSurcharge = this.allFuelSurchargeRates.find(
        (charge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate) =>
          charge.id === this.validToEdits.surchargeId,
      );
      if (foundFuelSurcharge !== undefined) {
        foundFuelSurcharge.validFromDate = this.validToEdits.validFromValue;
        this.saveValidFromToEdits(foundFuelSurcharge);
      }
    }
    this.savePrimaryFuelLevyRate(this.fuelSurchargeRate);
  }

  /**
   * Dispatches request to update Fuel Levy Rate, without waiting for the
   * response or doing anything afterwards
   * @param fuelSurchargeRate fuel levy to be saved
   */
  public saveValidFromToEdits(
    fuelSurchargeRate: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate,
  ) {
    if (this.isFleetAsset) {
      this.fuelLevyStore.saveFleetAssetFuelSurchargeRate(
        fuelSurchargeRate as FleetAssetFuelSurchargeRate,
      );
    } else {
      this.fuelLevyStore.saveClientFuelSurchargeRate(
        fuelSurchargeRate as ClientFuelSurchargeRate,
      );
    }
  }

  /**
   * Dispatches save request for Fuel Levy Rate.
   * @param fuelSurchargeRate fuel levy to be saved
   */
  public async savePrimaryFuelLevyRate(
    fuelSurchargeRate: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate,
  ) {
    let result: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate | null =
      null;
    const floatValue = fuelSurchargeRate.fuelSurchargeRate.toFixed(2);
    fuelSurchargeRate.fuelSurchargeRate = parseFloat(floatValue);
    if (this.isFleetAsset) {
      result = await this.fuelLevyStore.saveFleetAssetFuelSurchargeRate(
        fuelSurchargeRate as FleetAssetFuelSurchargeRate,
      );
    } else {
      result = await this.fuelLevyStore.saveClientFuelSurchargeRate(
        fuelSurchargeRate as ClientFuelSurchargeRate,
      );
    }
    if (result) {
      this.isNewFuelSurcharge = false;
      this.isEdited = false;
      this.isAwaitingSaveResponse = false;
      this.$emit('refreshFuelSurchargeRates');
    }
  }

  public editFuelSurcharge(
    fuelSurcharge: ClientFuelSurchargeRate | FleetAssetFuelSurchargeRate,
  ) {
    if (!this.isAuthorised()) {
      return;
    }
    this.isEdited = true;

    this.editedFuelSurchargeRate = JSON.parse(JSON.stringify(fuelSurcharge));
    this.$emit('setSelectedFuelSurchargeRate', fuelSurcharge);
  }

  get formIsValid(): boolean {
    const valid = this.$refs.fuelSurchargeForm;
    if (!valid) {
      return false;
    } else {
      return valid.validate();
    }
  }

  // Return status message to user depending on what view they are in. Status of active fuel surcharge/ creating/editing a fuel surcharge levy.
  get currentFuelSurchargeStatusMessage() {
    const activeFuelSurcharge = this.isFleetAsset
      ? this.allFuelSurchargeRates.find(
          (x: any) => x.tableId === this.activeFuelSurchargeTableId,
        )
      : this.activeFuelSurchargeRate;

    const currentDate = moment().valueOf();

    const isDivisionRate =
      !this.isDivision &&
      activeFuelSurcharge &&
      'clientId' in activeFuelSurcharge &&
      activeFuelSurcharge.clientId === '0';

    const isFleetRate =
      !this.isFleetAsset &&
      activeFuelSurcharge &&
      'fleetAssetId' in activeFuelSurcharge &&
      activeFuelSurcharge.fleetAssetId === '0';

    const isExpired =
      !this.isDivision &&
      activeFuelSurcharge &&
      activeFuelSurcharge.validToDate &&
      activeFuelSurcharge.validToDate <= currentDate;

    // ending message telling user whether the active rate is expired or a division rate.
    const endingMessage = isDivisionRate
      ? ' (Division)'
      : isExpired
        ? ' (Expired)'
        : isFleetRate
          ? '(Fleet Asset)'
          : '';

    return activeFuelSurcharge &&
      activeFuelSurcharge.validFromDate &&
      activeFuelSurcharge.validToDate
      ? 'Current Fuel Surcharge Rate is ' +
          activeFuelSurcharge.fuelSurchargeRate +
          '% from ' +
          returnFormattedDate(activeFuelSurcharge.validFromDate) +
          ' to ' +
          returnFormattedDate(activeFuelSurcharge.validToDate) +
          endingMessage
      : 'No active fuel surcharge rate exists.';
  }
  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }
}

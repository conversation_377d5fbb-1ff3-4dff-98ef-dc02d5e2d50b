<section class="fuel-surcharge-details">
  <v-layout justify-end class="mb-3">
    <v-btn
      depressed
      color="blue"
      class="mt-0"
      @click="newFuelSurcharge"
      :disabled="!isAuthorised()"
    >
      Create New
    </v-btn>
  </v-layout>
  <slot name="active-rates-summary"></slot>
  <v-data-table
    :headers="tableHeaders"
    class="gd-dark-theme mt-4"
    :items="allFuelSurchargeRatesReversed"
    no-data-text="No history of custom Fuel Surcharges available."
    hide-actions
  >
    <template v-slot:items="props">
      <tr @click="editFuelSurcharge(props.item)" style="cursor: pointer">
        <td
          :class="{activeDate: returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)}"
          class="pl-4"
        >
          {{props.item.fuelSurchargeRate}}%
        </td>
        <td
          :class="{activeDate: returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)}"
        >
          {{returnFormattedDate(props.item.validFromDate)}}
        </td>
        <td
          :class="{activeDate: returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)}"
        >
          {{returnFormattedDate(props.item.validToDate)}}
        </td>
        <td
          :class="{activeDate: returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)}"
        >
          <span
            v-if="returnDateActive(props.item.validFromDate, props.item.validToDate, props.item.tableId)"
            >Yes</span
          >
          <span v-else> No </span>
        </td>
      </tr>
    </template>
  </v-data-table>

  <v-dialog
    v-model="dialogController"
    width="520px"
    class="ma-0"
    content-class="v-dialog-custom"
    persistent
    no-click-animation
  >
    <v-flex
      md12
      class="app-theme__center-content--body"
      v-if="dialogController"
    >
      <v-layout
        justify-space-between
        align-center
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span
          >{{ viewType === 'NEW' ? `Create New Fuel Surcharge` : `Edit Fuel
          Surcharge`}}</span
        >
        <div
          class="app-theme__center-content--closebutton"
          @click="dialogController = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row class="pa-3">
        <v-flex md12>
          <v-form ref="fuelSurchargeForm">
            <v-alert
              :value="validFromEdits.edit || validToEdits.edit"
              type="warning"
              class="mb-4"
            >
              The selected dates overlap with an existing Fuel Surcharge. If you
              proceed, the overlapping Fuel Surcharges will have their dates
              adjusted to remove any crossover.
            </v-alert>
            <v-layout wrap>
              <v-flex md12>
                <v-layout>
                  <v-flex md5>
                    <v-layout align-center class="form-field-label-container">
                      <span
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >Fuel Surcharge Rate %</span
                      >
                    </v-layout>
                  </v-flex>
                  <v-flex md7>
                    <v-text-field
                      class="v-solo-custom"
                      solo
                      flat
                      type="number"
                      v-model.number="fuelSurchargeRate.fuelSurchargeRate"
                      :rules="[validate.required, validate.percentage, validate.nonNegative]"
                      autofocus
                    >
                    </v-text-field>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md5>
                    <v-layout align-center class="form-field-label-container">
                      <span
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >Valid From Date</span
                      >
                    </v-layout>
                  </v-flex>
                  <v-flex md7>
                    <DateTimeInputs
                      :epochTime.sync="validFromDate"
                      :enableValidation="true"
                      type="DATE_START_OF_DAY"
                      dateLabel="Valid From"
                      :readOnly="!isEdited"
                      :soloInput="true"
                      :boxInput="false"
                      :isRequired="true"
                      hintTextType="FORMATTED_SELECTION"
                      :minimumEpochTime="allowedDates.from.min"
                      :maximumEpochTime="allowedDates.from.max"
                      maxComparisonType="LESS_OR_EQUAL"
                    ></DateTimeInputs>
                  </v-flex>
                </v-layout>
              </v-flex>
              <v-flex md12>
                <v-layout>
                  <v-flex md5>
                    <v-layout align-center class="form-field-label-container">
                      <span
                        class="subheader--faded pr-3 pb-0 form-field-required-marker"
                        >Valid To Date</span
                      >
                    </v-layout>
                  </v-flex>
                  <v-flex md7>
                    <DateTimeInputs
                      :epochTime.sync="validToDate"
                      :enableValidation="true"
                      type="DATE_END_OF_DAY"
                      dateLabel="Valid To Date"
                      :readOnly="!isEdited"
                      :soloInput="true"
                      :boxInput="false"
                      :isRequired="true"
                      hintTextType="FORMATTED_SELECTION"
                      :minimumEpochTime="allowedDates.to.min"
                      :maximumEpochTime="allowedDates.to.max ? allowedDates.to.max : null"
                    ></DateTimeInputs>
                  </v-flex>
                </v-layout>
              </v-flex>
            </v-layout>
          </v-form>
        </v-flex>
      </v-layout>
      <v-divider></v-divider>
      <v-layout align-center>
        <v-btn flat color="red" @click="dialogController = false">Cancel</v-btn>
        <v-spacer></v-spacer>
        <!-- <v-btn outline color="white" @click="">Edit</v-btn> -->
        <v-btn
          depressed
          color="blue"
          class="v-btn-confirm-custom"
          @click="saveFuelSurcharge"
          :loading="awaitingSaveResponse"
          >Save</v-btn
        >
      </v-layout>
    </v-flex>
  </v-dialog>
</section>

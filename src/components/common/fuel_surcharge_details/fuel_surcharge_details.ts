import DateTimeInputs from '@/components/common/date-picker/date_time_inputs.vue';
import QuickSelectDateRange from '@/components/common/date-picker/quick_select_date_range.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { hasAdminOrHeadOfficeRole } from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import serviceTypeRates, {
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import FuelSurchargeRate from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { FuelSurchargeType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';

enum ViewType {
  EDIT = 'EDIT',
  NEW = 'NEW',
  TABLE = 'TABLE',
}
@Component({
  components: {
    QuickSelectDateRange,
    DateTimeInputs,
  },
})
export default class FuelSurchargeDetails
  extends Vue
  implements IUserAuthority
{
  @Prop() public fleetAssetId: string;
  @Prop() public clientId: string;
  @Prop() public allFuelSurchargeRates: FuelSurchargeRate[];
  @Prop({ default: FuelSurchargeType.FLEET_ASSET })
  public fuelSurchargeType: FuelSurchargeType;
  @Prop({ default: null }) public activeFuelSurchargeTableId: number | null;
  @Prop({ default: true }) public isEdited: boolean;

  public companyDetailsStore = useCompanyDetailsStore();

  public invalidManualEnter: boolean = false;

  public returnFormattedDate = returnFormattedDate;
  public awaitingSaveResponse: boolean = false;

  public viewType: ViewType = ViewType.TABLE;

  public serviceTypeRates: ServiceTypeRates[] = serviceTypeRates;

  // Contains the Fuel Surcharge that is displayed in form screen for editing
  public fuelSurchargeRate: FuelSurchargeRate | null = null;

  // Controls dialog visibility, and resets local working variables on close
  get dialogController() {
    return this.viewType === ViewType.NEW || this.viewType === ViewType.EDIT;
  }
  set dialogController(value: boolean) {
    if (!value) {
      this.cancelCreateOrEditFuelSurcharge();
    }
  }

  get isExpiredFuelSurcharge() {
    return this.activeFuelSurchargeTableId === null;
  }

  get activeFuelSurchargeRate(): FuelSurchargeRate | null {
    const activeId = this.activeFuelSurchargeTableId;
    if (activeId === null) {
      return null;
    }
    const foundActive = this.allFuelSurchargeRates.find(
      (r) => r.tableId === this.activeFuelSurchargeTableId,
    );
    return foundActive ? foundActive : null;
  }

  public tableHeaders: TableHeader[] = [
    {
      text: 'Rate',
      align: 'left',
      value: 'result',
      sortable: false,
    },
    {
      text: 'Valid From',
      align: 'left',
      value: 'result',
      sortable: false,
    },
    {
      text: 'Valid To',
      align: 'left',
      sortable: false,
      value: '',
    },
    {
      text: 'Active',
      align: 'left',
      sortable: false,
      value: '',
    },
  ];

  public $refs!: {
    fuelSurchargeForm: VForm;
  };

  get validate(): Validation {
    return validationRules;
  }

  // When editing a NEW or EXISTING FuelSurchargeRate, check adjacent rate
  // items, and return  the allowed dates/ranges that will be passed into the
  // DateTimeInputs components and used for validation
  get allowedDates() {
    if (this.fuelSurchargeRate === null) {
      return;
    }
    const tz = this.companyDetailsStore.userLocale;
    // Helper func to return date form
    const formatDate = (t: number) => moment(t).tz(tz).valueOf();

    const formatDateSub = (t: number) =>
      moment(t).tz(tz).subtract(1, 'day').valueOf();

    const formatDateAdd = (t: number) =>
      moment(t).tz(tz).add(1, 'day').valueOf();

    const min = moment.tz(tz).subtract(5, 'year');

    const fsr = this.fuelSurchargeRate;
    const latest = this.latestFuelSurcharge;
    const earliest = this.earliestFuelSurcharge;
    const prev = this.previousFuelSurcharge;
    const next = this.nextFuelSurcharge;

    let dates: any = {
      from: {
        min: min.valueOf(),
        max: !fsr || !fsr.validToDate ? undefined : formatDate(fsr.validToDate),
      },
      to: {
        min:
          !fsr || fsr.validFromDate === null
            ? undefined
            : formatDate(fsr.validFromDate),
        max: undefined,
      },
    };

    if (this.allFuelSurchargeRates.length === 0) {
      return dates;
    }
    // If EDITING existing charge,'
    if (this.viewType !== ViewType.EDIT) {
      dates = {
        from: {
          min:
            latest && latest.validFromDate
              ? formatDateAdd(latest.validFromDate)
              : undefined,
          max: !fsr.validToDate
            ? undefined
            : formatDateSub(fsr.validToDate + 1),
        },
        to: {
          min:
            fsr.validFromDate === null
              ? undefined
              : formatDate(fsr.validFromDate),
          max: undefined,
        },
      };
      return dates;
    }

    if (this.allFuelSurchargeRates.length > 1) {
      if (latest && fsr.id === latest.id) {
        dates = {
          from: {
            min:
              prev && prev.validFromDate
                ? formatDateAdd(prev.validFromDate)
                : undefined,
            max: !fsr.validToDate ? undefined : formatDate(fsr.validToDate),
          },
          to: {
            min:
              fsr.validFromDate === null
                ? undefined
                : formatDate(fsr.validFromDate),
            max: undefined,
          },
        };
      } else if (earliest && fsr.id === earliest.id) {
        dates = {
          from: {
            min: min.valueOf(),
            max: !fsr.validToDate ? undefined : formatDateSub(fsr.validToDate),
          },
          to: {
            min:
              fsr.validFromDate === null
                ? undefined
                : formatDate(fsr.validFromDate),
            max:
              next && next.validToDate
                ? formatDateSub(next.validToDate)
                : undefined,
          },
        };
      } else {
        dates = {
          from: {
            min:
              prev && prev.validFromDate
                ? formatDateAdd(prev.validFromDate)
                : undefined,
            max: !fsr.validToDate
              ? next && next.validFromDate
                ? formatDateSub(next.validFromDate)
                : undefined
              : formatDate(fsr.validToDate),
          },
          to: {
            min: !fsr.validFromDate ? undefined : formatDate(fsr.validFromDate),
            max:
              next && next.validToDate
                ? formatDateSub(next.validToDate)
                : undefined,
          },
        };
      }
    }
    return dates;
  }

  get previousFuelSurcharge() {
    if (this.fuelSurchargeRate === null) {
      return;
    }
    const id = this.fuelSurchargeRate.id;
    const foundEditedFuelSurcharge = this.allFuelSurchargeRates.find(
      (charge: FuelSurchargeRate) => charge.id === id,
    );
    if (foundEditedFuelSurcharge !== undefined) {
      const fuelSurcharge = this.allFuelSurchargeRates.find(
        (charge: FuelSurchargeRate) =>
          charge.validToDate! < foundEditedFuelSurcharge.validFromDate! &&
          charge.validToDate! >
            foundEditedFuelSurcharge.validFromDate! - 80400000,
      );
      if (fuelSurcharge !== undefined) {
        return fuelSurcharge;
      }
    } else {
      return this.latestFuelSurcharge;
    }
  }

  get nextFuelSurcharge() {
    if (this.fuelSurchargeRate === null) {
      return;
    }
    const id = this.fuelSurchargeRate.id;
    const foundEditedFuelSurcharge = this.allFuelSurchargeRates.find(
      (charge: FuelSurchargeRate) => charge.id === id,
    );
    if (foundEditedFuelSurcharge) {
      const fuelSurcharge = this.allFuelSurchargeRates.find(
        (charge: FuelSurchargeRate) =>
          charge.validFromDate! > foundEditedFuelSurcharge.validToDate! &&
          charge.validFromDate! <
            foundEditedFuelSurcharge.validToDate! + 80400000,
      );
      if (fuelSurcharge !== undefined) {
        return fuelSurcharge;
      }
    }
  }
  // Stores the editing to the FuelSurchargeRate that is BEFORE the currently
  // edited item. When saving the FuelSurchargeRate, these edit values will be
  // used to dispatch any edits required to the adjacent rates
  get validToEdits() {
    if (this.fuelSurchargeRate === null) {
      return;
    }
    const fsr = this.fuelSurchargeRate;
    const next = this.nextFuelSurcharge;
    const id = fsr.id;
    let foundEditedFuelSurcharge = this.allFuelSurchargeRates.find(
      (charge: FuelSurchargeRate) => charge.id === id,
    );

    if (this.viewType !== ViewType.EDIT) {
      foundEditedFuelSurcharge = fsr;
    }
    const requiredEdits: any = {
      edit: false,
      surchargeId: '',
      validFromValue: 0,
    };
    if (
      fsr.validToDate &&
      next &&
      next.validFromDate &&
      foundEditedFuelSurcharge &&
      foundEditedFuelSurcharge.validToDate
    ) {
      if (
        fsr.validToDate >= next.validFromDate ||
        fsr.validToDate < foundEditedFuelSurcharge.validToDate
      ) {
        requiredEdits.edit = true;
        requiredEdits.surchargeId = next.id;

        requiredEdits.validFromValue = moment(fsr.validToDate)
          .tz(this.companyDetailsStore.userLocale)
          .add(1, 'day')
          .startOf('day')
          .valueOf();
      }
    }
    return requiredEdits;
  }

  // Stores the editing to the FuelSurchargeRate that is BEFORE the currently
  // edited item. When saving the FuelSurchargeRate, these edit values will be
  // used to dispatch any edits required to the adjacent rates
  get validFromEdits() {
    if (this.fuelSurchargeRate === null) {
      return;
    }
    const id = this.fuelSurchargeRate.id;
    let foundEditedFuelSurcharge = this.allFuelSurchargeRates.find(
      (charge: FuelSurchargeRate) => charge.id === id,
    );

    if (this.viewType !== ViewType.EDIT) {
      foundEditedFuelSurcharge = this.fuelSurchargeRate;
    }
    const requiredEdits: any = {
      edit: false,
      surchargeId: '',
      validToValue: 0,
    };
    if (
      this.fuelSurchargeRate.validFromDate &&
      this.previousFuelSurcharge &&
      this.previousFuelSurcharge.validToDate &&
      foundEditedFuelSurcharge &&
      foundEditedFuelSurcharge.validFromDate
    ) {
      if (
        this.fuelSurchargeRate.validFromDate <=
          this.previousFuelSurcharge.validToDate ||
        this.fuelSurchargeRate.validFromDate >
          foundEditedFuelSurcharge.validFromDate
      ) {
        requiredEdits.edit = true;
        requiredEdits.surchargeId = this.previousFuelSurcharge.id;

        requiredEdits.validToValue = moment(
          this.fuelSurchargeRate.validFromDate,
        )
          .tz(this.companyDetailsStore.userLocale)
          .subtract(1, 'day')
          .endOf('day')
          .valueOf();
      }
    }
    return requiredEdits;
  }

  // Return the FuelSurchargeRate in allFuelSurchargeRates that has the lowest VALID FROM DATE
  get earliestFuelSurcharge() {
    if (
      !this.allFuelSurchargeRates ||
      !this.allFuelSurchargeRates.length ||
      !this.latestFuelSurcharge ||
      !this.latestFuelSurcharge.validFromDate
    ) {
      return;
    }
    return this.allFuelSurchargeRates.reduce((prev, curr) =>
      curr.validFromDate &&
      curr.validToDate &&
      curr.validFromDate < (prev.validFromDate ? prev.validFromDate : 0)
        ? curr
        : prev,
    );
  }

  // Return the FuelSurchargeRate in allFuelSurchargeRates that has the highest VALID TO DATE
  get latestFuelSurcharge() {
    if (!this.allFuelSurchargeRates || !this.allFuelSurchargeRates.length) {
      return;
    }
    return this.allFuelSurchargeRates.reduce((prev, curr) =>
      curr.validFromDate !== null &&
      curr.validToDate !== null &&
      curr.validToDate > (prev.validToDate ? prev.validToDate : 0)
        ? curr
        : prev,
    );
  }

  public returnDateActive(
    fromDate: number,
    toDate: number,
    tableId: number,
  ): boolean {
    if (tableId === this.activeFuelSurchargeTableId) {
      return true;
    }
    const currentTime = moment().valueOf();

    if (currentTime >= fromDate && currentTime <= toDate) {
      return true;
    } else {
      return false;
    }
  }
  // Returns current validFromDate from fuelSurchargeRate. Used as v-model for
  // DateTimeInput. Perform some additional logic when setting.
  get validFromDate(): number | null {
    if (this.fuelSurchargeRate === null) {
      return null;
    }
    return this.fuelSurchargeRate.validFromDate;
  }
  set validFromDate(epoch: number | null) {
    if (this.fuelSurchargeRate === null) {
      return;
    }
    this.fuelSurchargeRate.validFromDate = epoch;
  }
  // Returns current validFromDate from fuelSurchargeRate. Used as v-model for
  // DateTimeInput. Perform some additional logic when setting.
  get validToDate(): number | null {
    if (this.fuelSurchargeRate === null) {
      return null;
    }
    return this.fuelSurchargeRate.validToDate;
  }
  set validToDate(epoch: number | null) {
    if (this.fuelSurchargeRate === null) {
      return;
    }
    this.fuelSurchargeRate.validToDate = epoch;
  }

  // Set fuelSurchargeRate to clean rate, set few type and init with id
  public newFuelSurcharge() {
    this.viewType = ViewType.NEW;
    this.fuelSurchargeRate = new FuelSurchargeRate();
    if (this.latestFuelSurcharge && this.latestFuelSurcharge.validToDate) {
      this.fuelSurchargeRate.validFromDate = moment(
        this.latestFuelSurcharge.validToDate,
      )
        .tz(this.companyDetailsStore.userLocale)
        .add(1, 'day')
        .startOf('day')
        .valueOf();
    }
    if (this.fuelSurchargeType === FuelSurchargeType.FLEET_ASSET) {
      this.fuelSurchargeRate.fleetAssetId = this.fleetAssetId;
    } else {
      this.fuelSurchargeRate.clientId = this.clientId;
    }
  }

  // Clear working variable and set view back to TABLE
  public cancelCreateOrEditFuelSurcharge() {
    this.viewType = ViewType.TABLE;
    this.fuelSurchargeRate = null;
  }

  get allFuelSurchargeRatesReversed() {
    return [...this.allFuelSurchargeRates].reverse();
  }

  // Save the current editing FuelSurchargeRate, update any adjacent fuel
  // surcharges that overlap with the new values
  public async saveFuelSurcharge() {
    if (!this.$refs.fuelSurchargeForm.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }
    if (this.fuelSurchargeRate === null) {
      return;
    }
    this.awaitingSaveResponse = true;
    // If there were edits to the Fuel Surcharge BEFORE the edited item, then
    // save the updated value for that one
    if (this.validFromEdits.edit) {
      const foundFuelSurcharge = this.allFuelSurchargeRates.find(
        (charge: FuelSurchargeRate) =>
          charge.id === this.validFromEdits.surchargeId,
      );
      if (foundFuelSurcharge) {
        foundFuelSurcharge.validToDate = this.validFromEdits.validToValue;
        if (this.fuelSurchargeType === FuelSurchargeType.FLEET_ASSET) {
          useFuelLevyStore().saveFleetAssetFuelSurchargeRate(
            foundFuelSurcharge,
          );
        } else {
          useFuelLevyStore().saveClientFuelSurchargeRate(foundFuelSurcharge);
        }
      }
    }
    // If there were edits to the Fuel Surcharge AFTER the edited item, then
    // save the updated value for that one
    if (this.validToEdits.edit) {
      const foundFuelSurcharge = this.allFuelSurchargeRates.find(
        (charge: FuelSurchargeRate) =>
          charge.id === this.validToEdits.surchargeId,
      );
      if (foundFuelSurcharge !== undefined) {
        foundFuelSurcharge.validFromDate = this.validToEdits.validFromValue;
        if (this.fuelSurchargeType === FuelSurchargeType.FLEET_ASSET) {
          useFuelLevyStore().saveFleetAssetFuelSurchargeRate(
            foundFuelSurcharge,
          );
        } else {
          useFuelLevyStore().saveClientFuelSurchargeRate(foundFuelSurcharge);
        }
      }
    }
    let saved = false;

    // Save the edited FuelSurchargeRate and await responses. On successful
    // response, emit to parent to refresh the list of FuelSurchargeRates
    if (this.fuelSurchargeType === FuelSurchargeType.FLEET_ASSET) {
      saved = await this.fuelSurchargeRate.saveFleetAsset();
    } else {
      const floatValue = this.fuelSurchargeRate.fuelSurchargeRate.toFixed(2);
      this.fuelSurchargeRate.fuelSurchargeRate = parseFloat(floatValue);
      saved = await this.fuelSurchargeRate.saveClient();
    }
    if (saved) {
      this.viewType = ViewType.TABLE;
      this.awaitingSaveResponse = false;
      this.$emit('refreshServiceRateList');
    }
  }

  // Copy the selected FuelSurchargeRate to the local working variable, then
  // change the view to the edit more
  public editFuelSurcharge(fuelSurcharge: FuelSurchargeRate) {
    if (!this.isAuthorised()) {
      return;
    }
    this.viewType = ViewType.EDIT;
    this.fuelSurchargeRate = Object.assign(
      new FuelSurchargeRate(),
      fuelSurcharge,
    );
  }
  // Validation handler for form
  get formIsValid(): boolean {
    const valid = this.$refs.fuelSurchargeForm;
    if (!valid) {
      return false;
    } else {
      return valid.validate();
    }
  }

  // Return status message to user depending on what view they are in. Status of active fuel surcharge/ creating/editing a fuel surcharge levy.
  get currentFuelSurchargeStatusMessage() {
    if (this.viewType === ViewType.NEW) {
      return 'Please enter details for the new fuel surcharge levy.';
    }
    if (this.viewType === ViewType.EDIT) {
      return 'Please remember to save your changes once you have finished editing this fuel surcharge levy.';
    }
  }

  public isAuthorised(): boolean {
    return hasAdminOrHeadOfficeRole();
  }

  public findOrReplaceFuelSurcharge(fuel: FuelSurchargeRate) {
    if (fuel === null || !fuel.id) {
      return;
    }
    const fuelSurcharge: FuelSurchargeRate = Object.assign(
      new FuelSurchargeRate(),
      fuel,
    );
    const foundIndex = this.allFuelSurchargeRates.findIndex(
      (f) => f.id === fuel.id,
    );
    if (foundIndex !== -1) {
      this.allFuelSurchargeRates.splice(foundIndex, 1, fuelSurcharge);
      showNotification('Fuel Surcharge updated.', {
        type: HealthLevel.SUCCESS,
      });
    } else {
      this.allFuelSurchargeRates.push(fuelSurcharge);
      showNotification('Fuel Surcharge saved.', { type: HealthLevel.SUCCESS });
    }
  }
}

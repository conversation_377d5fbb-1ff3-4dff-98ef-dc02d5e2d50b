import AddressSearchAU from '@/components/common/addressing/address-search-au/index.vue';
// import AddPudItem from '@/components/operations/BookJob/add_pud_item.vue';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { initialisePudItem } from '@/helpers/classInitialisers/InitialisePudItem';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Validation } from '@/interface-models/Generic/Validation';
import { JobOperationType } from '@/interface-models/Jobs/JobOperationType';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { PudMaintenanceDialogConfig } from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceDialogConfig';
import { PudMaintenanceType } from '@/interface-models/Jobs/PUD/UnassignedPudItem/PudMaintenanceType';
import UnassignedPudItemResponse from '@/interface-models/Jobs/PUD/UnassignedPudItem/SaveUnassignedPudItemResponse';
import UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface AwaitingResponses {
  clientDetails: boolean;
  clientServiceRate: boolean;
  clientFuelSurcharge: boolean;
  unassignedPudSave: boolean;
}
@Component({
  components: { AddressSearchAU },
})
export default class UnassignedPudMaintenance extends Vue {
  @Prop({ default: '' }) public buttonText: string;
  @Prop({ default: false }) public buttonDisabled: boolean;
  @Prop({ default: '' }) public faIconName: string;
  @Prop({ default: false }) public isIcon: boolean;
  @Prop({ default: false }) public isListTile: boolean;
  @Prop({ default: false }) public useLeadingIcon: boolean;
  @Prop({ default: false }) public showPudMaintenanceDialog: boolean;

  public dataImportStore = useDataImportStore();
  public serviceRateStore = useServiceRateStore();
  public operationsStore = useOperationsStore();
  public clientDetailsStore = useClientDetailsStore();

  public pudMaintenanceType: PudMaintenanceType = PudMaintenanceType.JOB;
  public operationType: JobOperationType = JobOperationType.NEW;

  public editingPudItem: PUDItem = new PUDItem();
  public unassignedPudItem: UnassignedPudItem = new UnassignedPudItem();

  public clientId: string = '';
  public clientDetails: ClientDetails | null = null;

  public mergedClientServiceRateTable: ClientServiceRate | null;
  public clientFuelSurcharge: ClientFuelSurchargeRate | null;

  public awaitingResponses: AwaitingResponses = {
    clientDetails: false,
    clientServiceRate: false,
    clientFuelSurcharge: false,
    unassignedPudSave: false,
  };

  public unassignedPudItems: UnassignedPudItem[] = [];
  public legTypes: any[] = [
    { id: 'P', value: 'Pickup' },
    { id: 'D', value: 'Dropoff' },
  ];
  public showUnitRateInputs: boolean = false;

  public returnFormattedDate = returnFormattedDate;

  get validationRules(): Validation {
    return validationRules;
  }

  public validationHandler(): boolean {
    const form: any = this.$refs.pudMaintenanceForm;
    if (!form || !form.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return false;
    }
    return true;
  }

  get showDialog() {
    return this.showPudMaintenanceDialog;
  }
  set showDialog(value: boolean) {
    this.operationsStore.setViewingPudMaintenanceDialog(value);
  }

  public closeDialog(): void {
    this.showDialog = false;
  }

  public payloadDimensionDisplay(dimension: string) {
    return dimension && dimension !== '' ? dimension + 'm' : ' - ';
  }

  public currentClientFuelSurcharge(
    clientFuelSurcharge: ClientFuelSurchargeRate,
  ) {
    this.clientFuelSurcharge = clientFuelSurcharge;
  }

  public setCurrentActiveMergedServiceRate(
    mergedServiceRate: ClientServiceRate,
  ) {
    this.mergedClientServiceRateTable = Object.assign(
      new ClientServiceRate(),
      mergedServiceRate,
    );
    this.awaitingResponses.clientFuelSurcharge = false;
  }

  get validServiceTypes() {
    if (!this.mergedClientServiceRateTable) {
      return [];
    }
    return this.mergedClientServiceRateTable.availableServiceTypes;
  }

  get allDataLoaded(): boolean {
    const clientDataArrived =
      this.clientId !== '' &&
      this.clientDetails !== null &&
      this.mergedClientServiceRateTable !== null &&
      this.clientFuelSurcharge !== null;
    return (
      this.unassignedPudItem &&
      this.editingPudItem.legTypeFlag !== '' &&
      clientDataArrived
    );
  }

  get rateToApply() {
    if (!this.mergedClientServiceRateTable) {
      return null;
    }
    return this.mergedClientServiceRateTable.rateToApplyToJob(-1, 5);
  }

  get clientList(): ClientSearchSummary[] {
    const allClients = this.clientDetailsStore.clientSummaryList;
    return allClients.filter((x: any) => !x.statusList.includes(13));
  }

  get allUnassignedLegs() {
    return [];
  }

  public async getSelectedClientDetails(clientId: string) {
    this.awaitingResponses.clientDetails = true;
    this.awaitingResponses.clientServiceRate = true;
    this.awaitingResponses.clientFuelSurcharge = true;

    const searchDate = moment().valueOf();
    this.clientId = clientId;
    // Request ClientDetails,  client service rates and fuel. Try first to find
    // client's custom rates first. If not found, tries to find the division
    // defaults
    const [clientDetails, { clientRates, clientFuelRate }] = await Promise.all([
      this.clientDetailsStore.requestClientDetailsByClientId(clientId),
      this.serviceRateStore.getCurrentRatesAndFuelForClientId(
        clientId,
        searchDate,
      ),
    ]);

    // Handle responses
    if (clientDetails) {
      this.clientDetails = clientDetails;
      this.awaitingResponses.clientDetails = false;
    }
    if (clientRates?.clientServiceRate) {
      this.setCurrentActiveMergedServiceRate(clientRates.clientServiceRate);
    }
    if (clientFuelRate) {
      this.currentClientFuelSurcharge(clientFuelRate);
    }
  }

  // Handle response from AddPudItem component
  // Use config to decide what needs to occur on the save
  public savePudItem(pudItem: PUDItem) {
    // UNASSIGNED PUD ITEM
    if (this.pudMaintenanceType === PudMaintenanceType.UNASSIGNED) {
      switch (this.operationType) {
        case JobOperationType.NEW:
          // ADDING NEW UnassignedPudItem
          this.saveNewUnassignedPudItem(pudItem);
          break;
        case JobOperationType.EDIT:
          // EDITING EXISTING PUD
          this.saveEditedUnassignedPudItem(pudItem);
          break;
        case JobOperationType.VIEW:
          // READONLY VIEW OF PUD ITEM
          break;
      }
    }

    // Normal PUD ITEM
    if (this.pudMaintenanceType === PudMaintenanceType.JOB) {
      switch (this.operationType) {
        case JobOperationType.NEW:
          // ADDING NEW PUD TO JOB
          break;
        case JobOperationType.EDIT:
          // EDITING EXISTING PUD
          break;
        case JobOperationType.VIEW:
          // READONLY VIEW OF PUD ITEM
          break;
      }
    }
  }
  // Combine pudDetails with the unassignedPudItem object, then
  // Call save ACTION from DataImportModule
  public async saveNewUnassignedPudItem(pudItem: PUDItem) {
    if (!this.validationHandler()) {
      return;
    }
    this.unassignedPudItem.company = sessionManager.getCompanyId();
    this.unassignedPudItem.division = sessionManager.getDivisionId();
    this.unassignedPudItem.createdBy = sessionManager.getActiveUser();
    this.awaitingResponses.unassignedPudSave = true;
    this.unassignedPudItem.pudDetails = pudItem;
    this.handleSavedUnassignedPudItem(
      await this.dataImportStore.saveUnassignedPudItem(this.unassignedPudItem),
    );
  }
  // Combine pudDetails with the unassignedPudItem object, then
  // Call save ACTION from DataImportModule
  public async saveEditedUnassignedPudItem(pudItem: PUDItem) {
    if (!this.validationHandler()) {
      return;
    }
    this.awaitingResponses.unassignedPudSave = true;
    this.unassignedPudItem.pudDetails = pudItem;
    this.handleSavedUnassignedPudItem(
      await this.dataImportStore.saveUnassignedPudItem(this.unassignedPudItem),
    );
  }

  /**
   * Handles response from saveUnassignedPudItem action. Shows a notification if
   * the incoming response was booked by the current user
   * @param response - UnassignedPudItemResponse containing saved unassigned pud
   */
  private handleSavedUnassignedPudItem(
    response: UnassignedPudItemResponse | null,
  ) {
    if (
      response?.unassignedPudItemList.length === 1 &&
      response.unassignedPudItemList[0].createdBy ===
        sessionManager.getActiveUser()
    ) {
      showNotification('Point Saved Successfully', {
        type: HealthLevel.SUCCESS,
      });
      this.closeDialog();
    }
  }

  public addNewUnassignedPudItem() {
    this.unassignedPudItem = new UnassignedPudItem();
    this.editingPudItem = new PUDItem();
    this.editingPudItem.timeDefinition = 0;
  }

  public editExistingUnassignedPudItem(config: PudMaintenanceDialogConfig) {
    if (!config.unassignedPudId) {
      // ERROR
      console.error('unassignedPudId is undefined');
      return;
    }
    let foundUnassignedPudItem;
    if (config.unassignedPudItem) {
      foundUnassignedPudItem = config.unassignedPudItem;
    } else {
      foundUnassignedPudItem = this.dataImportStore.unassignedPudItemList.find(
        (u) => u.id === config.unassignedPudId,
      );
    }
    if (!foundUnassignedPudItem) {
      console.error('Could not find associated UnassignedPudItem');
      // ERROR
      return;
    }
    this.unassignedPudItem = Object.assign(
      new UnassignedPudItem(),
      foundUnassignedPudItem,
    );
    this.editingPudItem = initialisePudItem(this.unassignedPudItem.pudDetails);
    if (
      this.editingPudItem.rateDetails.unitPickUps !== 0 ||
      this.editingPudItem.rateDetails.unitDropOffs !== 0
    ) {
      this.showUnitRateInputs = true;
    }
    this.getSelectedClientDetails(foundUnassignedPudItem.clientId);
  }

  public viewUnassignedPudItem(config: PudMaintenanceDialogConfig) {
    if (!config.unassignedPudId) {
      // ERROR
      console.error('unassignedPudId is undefined');
      return;
    }
    let foundUnassignedPudItem;
    if (config.unassignedPudItem) {
      foundUnassignedPudItem = config.unassignedPudItem;
    } else {
      foundUnassignedPudItem = this.dataImportStore.unassignedPudItemList.find(
        (u) => u.id === config.unassignedPudId,
      );
    }
    if (!foundUnassignedPudItem) {
      console.error('Could not find associated UnassignedPudItem');
      // ERROR
      return;
    }
    this.unassignedPudItem = Object.assign(
      new UnassignedPudItem(),
      foundUnassignedPudItem,
    );

    this.editingPudItem = initialisePudItem(this.unassignedPudItem.pudDetails);
    if (
      this.editingPudItem.rateDetails.unitPickUps !== 0 ||
      this.editingPudItem.rateDetails.unitDropOffs !== 0
    ) {
      this.showUnitRateInputs = true;
    }
    this.getSelectedClientDetails(foundUnassignedPudItem.clientId);
  }

  // Used to initialise component variables based on config (from OperationsModule)
  public initialiseDialogFromConfig(config: PudMaintenanceDialogConfig) {
    this.pudMaintenanceType = config.pudType;
    this.operationType = config.operationType;
    // Check config values and initialise values accordingly
    // UNASSIGNED PUD ITEM
    if (config.pudType === PudMaintenanceType.UNASSIGNED) {
      switch (config.operationType) {
        case JobOperationType.NEW:
          // ADDING NEW UnassignedPudItem
          this.addNewUnassignedPudItem();
          break;
        case JobOperationType.EDIT:
          // EDITING EXISTING PUD
          this.editExistingUnassignedPudItem(config);
          break;
        case JobOperationType.VIEW:
          // READONLY VIEW OF PUD ITEM
          this.viewUnassignedPudItem(config);
          break;
      }
    }

    // Normal PUD ITEM
    if (config.pudType === PudMaintenanceType.JOB) {
      switch (config.operationType) {
        case JobOperationType.NEW:
          // ADDING NEW PUD TO JOB
          break;
        case JobOperationType.EDIT:
          // EDITING EXISTING PUD
          break;
        case JobOperationType.VIEW:
          // READONLY VIEW OF PUD ITEM
          break;
      }
    }
  }

  public mounted() {
    const config = this.operationsStore.pudMaintenanceDialogConfig;
    if (config !== null) {
      this.initialiseDialogFromConfig(config);
    } else {
      this.operationsStore.setViewingPudMaintenanceDialog(false);
    }
  }
}

<template>
  <div class="division-fuel-surcharge-summary" align-center>
    <v-layout row wrap v-if="!props.isAppStatusBar">
      <v-flex md12 class="item-container">
        <span class="summary-header">Status:</span
        ><span
          class="summary-value"
          :class="
            fuelSurchargeRateStatus === 'INACTIVE'
              ? 'error-text'
              : 'success-text'
          "
          >{{ fuelSurchargeRateStatus }}</span
        >
      </v-flex>
      <v-flex md12 class="item-container">
        <span class="summary-header">Rate:</span
        ><span class="summary-value">{{ fuelSurchargeRate }}</span>
      </v-flex>
      <v-flex md12 class="item-container">
        <span class="summary-header">Valid From:</span
        ><span class="summary-value">{{ fuelSurchargeValidFromDate }}</span>
      </v-flex>
      <v-flex md12 class="item-container">
        <span class="summary-header">Valid To:</span
        ><span class="summary-value">{{ fuelSurchargeValidToDate }}</span>
      </v-flex>
      <v-flex md12 class="item-container">
        <span class="summary-header">Active Days Remaining:</span
        ><span
          class="summary-value"
          :class="
            activeDaysRemaining === 0
              ? 'error-text'
              : activeDaysRemaining <= 5
                ? 'warning-text'
                : ''
          "
          >{{ activeDaysRemaining }}
        </span>
      </v-flex>
    </v-layout>
    <v-layout v-else align-center fill-height>
      <!-- <span class="appbar-label-text">Fuel Levy:</span>  -->

      <v-tooltip bottom max-width="350px">
        <template v-slot:activator="{ on }">
          <div
            v-on="on"
            class="appbar-label-text"
            :class="{ 'error-type': !activeDivisionFuelSurchargeRate }"
          >
            {{
              activeDivisionFuelSurchargeRate
                ? fuelSurchargeRate
                : fuelSurchargeRateStatus
            }}
            <v-icon
              size="18"
              :color="
                activeDivisionFuelSurchargeRate ? 'green accent-3' : 'white'
              "
              >far fa-gas-pump</v-icon
            >
          </div>
        </template>
        <span v-if="activeDivisionFuelSurchargeRate"
          ><p class="mb-1">
            Current Fuel Levy:
            <strong
              >{{ activeDivisionFuelSurchargeRate.fuelSurchargeRate }}%</strong
            >
          </p>
          Valid:
          {{
            returnFormattedDate(
              activeDivisionFuelSurchargeRate.validFromDate
                ? activeDivisionFuelSurchargeRate.validFromDate
                : 0,
            )
          }}
          -
          {{
            returnFormattedDate(
              activeDivisionFuelSurchargeRate.validToDate
                ? activeDivisionFuelSurchargeRate.validToDate
                : 0,
            )
          }}
        </span>
        <span v-else>
          There is no current Division Fuel Levy. This may impact your ability
          to book and price jobs. Please contact Head Office.
        </span>
      </v-tooltip>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';

import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted } from 'vue';

const props = defineProps<{
  isAppStatusBar: boolean;
}>();

const companyDetailsStore = useCompanyDetailsStore();

const activeDivisionFuelSurchargeRate: ComputedRef<ClientFuelSurchargeRate | null> =
  computed((): ClientFuelSurchargeRate | null => {
    return companyDetailsStore.activeDivisionFuelSurchargeRate;
  });

const fuelSurchargeRate = computed(() => {
  if (!activeDivisionFuelSurchargeRate.value) {
    return '-';
  }
  return activeDivisionFuelSurchargeRate.value?.fuelSurchargeRate + '%';
});

const fuelSurchargeValidFromDate: ComputedRef<string> = computed((): string => {
  if (
    !activeDivisionFuelSurchargeRate.value ||
    activeDivisionFuelSurchargeRate.value.validFromDate === null
  ) {
    return '-';
  }

  return returnFormattedDate(
    activeDivisionFuelSurchargeRate.value.validFromDate,
  );
});

const fuelSurchargeValidToDate: ComputedRef<string> = computed((): string => {
  if (
    !activeDivisionFuelSurchargeRate.value ||
    activeDivisionFuelSurchargeRate.value.validToDate === null
  ) {
    return '-';
  }

  return returnFormattedDate(activeDivisionFuelSurchargeRate.value.validToDate);
});

const fuelSurchargeRateStatus: ComputedRef<string> = computed((): string => {
  if (!activeDivisionFuelSurchargeRate.value) {
    return 'INACTIVE';
  }
  return 'ACTIVE';
});

// returns the number of days until the fuel surcharge rate expires
const activeDaysRemaining: ComputedRef<number> = computed((): number => {
  if (!activeDivisionFuelSurchargeRate.value) {
    return 0;
  }
  const currentTime = moment.tz(companyDetailsStore.userLocale);
  const validToDate = moment(
    activeDivisionFuelSurchargeRate.value.validToDate,
  ).tz(companyDetailsStore.userLocale);
  const duration = moment.duration(validToDate.diff(currentTime));
  return Math.floor(duration.asDays());
});

onMounted(() => {
  if (companyDetailsStore.activeDivisionFuelSurchargeRate === null) {
    companyDetailsStore.initActiveDivisionFuelSurchargeRate();
  }
});
</script>

<style scoped lang="scss">
.division-fuel-surcharge-summary {
  display: flex;
  align-items: center;
  height: 100%;
  .summary-header {
    margin: 18px;
    font-size: $font-size-20;
  }

  .summary-value {
    margin: 18px;
    font-weight: 500;
    font-size: $font-size-20;
  }

  .item-container {
    border-top: 0.5px solid $border-color;
    width: 100%;
    display: flex;
    height: 55px;
    justify-content: space-between;
    align-items: center;
  }

  .appbar-label-text {
    font-family: $sub-font-family;
    font-size: $font-size-20;
    font-weight: 500;
    padding: 2px 12px;

    &:hover {
      filter: brightness(120%);
      background-color: rgba(255, 255, 255, 0.1);
      cursor: context-menu;
    }

    &.error-type {
      background-color: rgb(255, 0, 0);
      border-radius: 2px;
      font-weight: 800;
      &:hover {
        filter: brightness(120%);
        cursor: context-menu;
      }
    }
  }
}
</style>

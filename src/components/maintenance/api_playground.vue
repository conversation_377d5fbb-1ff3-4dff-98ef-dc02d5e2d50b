<template>
  <div class="api-playground pa-4">
    <v-layout class="mb-4">
      <p class="title ma-0">API Playground</p>
      <v-spacer></v-spacer>
      <v-btn
        outline
        color="white"
        @click="loadFromLocalStorage"
        class="v-btn-confirm-custom my-0"
        >Load Last Request</v-btn
      >
      <!-- <v-btn
      depressed
      color="blue"
      @click="sendCustomRequest"
      class="v-btn-confirm-custom ma-0"
      >Send Custom Request (see code)</v-btn
    > -->
    </v-layout>
    <v-divider></v-divider>
    <v-layout>
      <v-flex md6 px-2>
        <v-layout pt-2><span class="subheader--bold">Request</span></v-layout>

        <v-text-field
          label="Request ID"
          class="v-solo-custom"
          v-model="requestId"
          solo
          flat
          hint="Include leading forward slash. Example: /job/saveJob"
        >
        </v-text-field>

        <v-textarea
          class="v-solo-custom"
          solo
          rows="30"
          flat
          label="Request Payload"
          v-model="requestPayload"
        ></v-textarea>

        <v-layout align-center justify-space-between>
          <v-btn outline color="white" @click="clearRequestInputs">Clear</v-btn>
          <v-btn outline color="white" @click="setRequestDefaults"
            >Set Defaults</v-btn
          >
          <v-spacer></v-spacer>

          <v-btn
            depressed
            color="blue"
            @click="sendRequest"
            class="v-btn-confirm-custom"
            >Send</v-btn
          >
        </v-layout>
        <v-layout>
          <v-flex md8>
            <v-checkbox
              v-model="stringifyRequest"
              color="light-blue"
              class="mt-2"
              label="Stringify Request"
            />
          </v-flex>
        </v-layout>
      </v-flex>
      <v-divider vertical></v-divider>
      <v-flex md6 px-2>
        <v-layout pt-2><span class="subheader--bold">Response</span></v-layout>
        <v-text-field
          label="Response ID"
          class="v-solo-custom"
          v-model="responseId"
          solo
          flat
        ></v-text-field>
        <v-textarea
          class="v-solo-custom"
          solo
          rows="30"
          flat
          label="Response Payload"
          v-model="responsePayload"
        ></v-textarea>
        <v-layout align-center justify-space-between>
          <v-btn outline color="white" @click="clearResponseInputs"
            >Clear Response Payload</v-btn
          >
        </v-layout>
      </v-flex>
    </v-layout>
  </div>
</template>

<script setup lang="ts">
import Environment from '@/configuration/environment';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { useDynamicRequestStore } from '@/store/modules/DynamicRequestStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import Mitt, { WebsocketEvents } from '@/utils/mitt';
import { computed, Ref, ref, WritableComputedRef } from 'vue';

/**
 * Indicates if the API playground is awaiting a response.
 */
const isAwaitingResponse: Ref<boolean> = ref(false);

/**
 * The request ID for the API call.
 */
const requestId: Ref<string> = ref('');

/**
 * Whether to stringify the request payload.
 */
const stringifyRequest: Ref<boolean> = ref(true);

/**
 * The request payload as a string.
 */
const requestPayload: Ref<string> = ref('');

/**
 * The response payload as a string.
 */
const responsePayload: Ref<string> = ref('');

/**
 * Computed getter/setter for the response ID, synced with the dynamic request store.
 */
const responseId: WritableComputedRef<keyof WebsocketEvents | null> = computed({
  get(): keyof WebsocketEvents | null {
    return useDynamicRequestStore().apiPlaygroundResponseId;
  },
  set(value: keyof WebsocketEvents | null): void {
    useDynamicRequestStore().setApiPlaygroundResponseId(value);
  },
});

/**
 * Clears all request-related inputs.
 */
function clearRequestInputs(): void {
  requestId.value = '';
  requestPayload.value = '';
  responseId.value = null;
  responsePayload.value = '';
  stringifyRequest.value = true;
}

/**
 * Clears only the response payload input.
 */
function clearResponseInputs(): void {
  responsePayload.value = '';
}

/**
 * Sets default values for the request inputs.
 */
function setRequestDefaults(): void {
  requestPayload.value = formatJson(null);
  requestId.value = '/additionalChargeItem/get/listAll';
  responseId.value = 'selectAdditionalChargeItemList';
}

/**
 * Formats a JSON object as a pretty-printed string.
 * @param json - The JSON object to format.
 * @returns The formatted JSON string.
 */
function formatJson(json: any): string {
  return JSON.stringify(json, undefined, 4);
}

/**
 * Sets up a Mitt event listener for the current response ID.
 * When triggered, updates the response payload and disables awaiting state.
 */
function setMittListener(): void {
  if (responseId.value !== null) {
    const callback = (data: any) => {
      if (responseId.value !== null) {
        responsePayload.value = formatJson(data);
        isAwaitingResponse.value = false;
        Mitt.off(responseId.value, callback);
      }
    };
    Mitt.on(responseId.value, callback);
  }
}

function saveToLocalStorage() {
  const data = {
    requestPayload: requestPayload.value,
    requestId: requestId.value,
    responseId: responseId.value,
  };
  localStorage.setItem('apiPlaygroundData', JSON.stringify(data));
}

function loadFromLocalStorage() {
  const data = localStorage.getItem('apiPlaygroundData');
  if (data) {
    const parsedData = JSON.parse(data);
    requestPayload.value = parsedData.requestPayload || '';
    requestId.value = parsedData.requestId || '';
    responseId.value = parsedData.responseId || '';
  }
}

/**
 * Sends the API request using the current request ID and payload.
 * Handles environment restrictions, JSON parsing, and error notifications.
 */
function sendRequest(): void {
  const ENV: string | undefined = Environment.value('environment');
  if (!['local', 'staging'].includes(ENV)) {
    alert('Forbidden');
    return;
  }

  try {
    const removeSpaces: string = requestPayload.value.replace(
      /(\r\n|\n|\r)/g,
      '',
    );
    const replaceQuotes: string = removeSpaces.replace(/“|”/g, '"');
    const parsed: any = stringifyRequest.value
      ? JSON.parse(replaceQuotes)
      : replaceQuotes;
    if (requestId.value) {
      requestPayload.value = formatJson(parsed);
      isAwaitingResponse.value = true;

      // Save to local storage before sending the request
      saveToLocalStorage();

      useWebsocketStore().sendWebsocketRequest(
        new WebSocketRequest(requestId.value, parsed, stringifyRequest.value),
      );
      setMittListener();
    }
  } catch (e) {
    showNotification(
      'Something went wrong. Please see console logs for more details',
    );
    console.error(e);
  }
}

// /**
//  * Sends a custom request to fetch the current client fuel surcharge.
//  * Updates the response payload with the result.
//  */
// async function sendCustomRequest(): Promise<void> {
//   const result = await useFuelLevyStore().getCurrentClientFuelSurcharge(
//     '0',
//     moment().valueOf(),
//   );
//   responsePayload.value = formatJson(result);
// }
</script>
<style scoped lang="scss"></style>

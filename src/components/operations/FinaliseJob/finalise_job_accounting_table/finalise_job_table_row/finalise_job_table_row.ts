import {
  getPercentageOf,
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  nearestFutureMinutes,
  nearestMinutes,
  nearestPastMinutes,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { isDistanceRateData } from '@/helpers/RateHelpers/RateDataHelpers';
import { isDistanceRateTypeObject } from '@/helpers/RateHelpers/RateTableItemHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import FinishedJobData from '@/interface-models/Generic/Accounting/FinishedJobDetails/FinishedJobData';
import { AdditionalChargeSubtotal } from '@/interface-models/Generic/Accounting/JobAccountingTotals/AdditionalChargeSubtotal';
import JobAccountingMargin from '@/interface-models/Generic/Accounting/JobAccountingTotals/JobAccountingMargin';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import {
  GraceTypes,
  graceTypes,
} from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import {
  RateMultipliers,
  rateMultipliers,
} from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import UnitRateData from '@/interface-models/Jobs/FinishedJobDetails/UnitRateData';
import ZonedUnitRateData from '@/interface-models/Jobs/FinishedJobDetails/ZonedUnitRateData';
import ZoneRateData from '@/interface-models/Jobs/FinishedJobDetails/zoneRateData';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import { FuelSurchargeBreakdown } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeBreakdown';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import RangedRate from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RangedRate';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import TripRate from '@/interface-models/ServiceRates/ServiceTypes/TripRate/TripRate';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import unitRateTypes from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRateTypes';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useRootStore } from '@/store/modules/RootStore';
import moment from 'moment-timezone';
import { Component, Prop, Vue } from 'vue-property-decorator';

interface ClientAndFleetAsset {
  client: string;
  fleetAsset: string;
}
@Component({
  components: {},
})
export default class FinaliseJobTableRow extends Vue {
  @Prop() public chargeItem:
    | AdditionalChargeItem
    | ClientFuelSurchargeRate
    | JobPrimaryRate;
  @Prop() public secondaryChargeItem: JobPrimaryRate;

  @Prop({ default: false }) public fuelLevyType: boolean;
  @Prop({ default: false }) public additionalChargeType: boolean;
  @Prop({ default: false }) public freightChargeType: boolean;
  @Prop({ default: false }) public outsideMetroType: boolean;
  @Prop() public clientRate: any;
  @Prop() public fleetAssetRate: any;
  @Prop() public job: JobDetails;
  @Prop() public fleetAssetFuelSurcharge: FleetAssetFuelSurchargeRate;
  @Prop({ default: true }) public clientGstRegistered: boolean;
  @Prop() public fleetAssetGstRegistered: boolean;
  @Prop() public finishedJobData: FinishedJobData;
  @Prop({ default: false }) public isStandbyRate: boolean;
  @Prop() public isEquipmentHire: boolean;
  @Prop() public outsideMetroCharge: any;
  @Prop({ default: false }) public isDemurrageRate: boolean;
  public unitRateTypes: ShortLongName[] = unitRateTypes;
  public rateMultipliers: RateMultipliers[] = rateMultipliers;
  public graceTypes: GraceTypes[] = graceTypes;
  public roundCurrencyValue = RoundCurrencyValue;
  public displayCurrencyValue = DisplayCurrencyValue;

  public nearestFutureMinutes = nearestFutureMinutes;
  public nearestPastMinutes = nearestPastMinutes;
  public nearestMinutes = nearestMinutes;

  public applicableFuelSurcharges: ShortLongName[] = applicableFuelSurcharges;
  get additionalChargeItems(): AdditionalChargeItem[] {
    return useRootStore().additionalChargeItemList;
  }
  get additionalChargeTypes(): AdditionalChargeType[] {
    return useRootStore().additionalChargeTypeList;
  }

  public returnAdditionalChargeType(id: string, typeId: string): string {
    const foundChargeItem = this.additionalChargeItems.find(
      (item) => item._id === id,
    );
    if (foundChargeItem) {
      return foundChargeItem.longName;
    } else {
      const foundChargeType = this.additionalChargeTypes.find(
        (x: AdditionalChargeType) => x._id === typeId,
      );

      if (foundChargeType) {
        return foundChargeType.longName;
      }
    }
    return '';
  }

  get clientAppliedFuelSurchargeType(): string {
    if (!this.fuelLevyType || this.job.accounting.clientRates.length === 0) {
      return '';
    }
    const rate: RateTableItems = this.job.accounting.clientRates[0].rate;
    return this.getAppliedFuelSurchargeTypeDisplayName(rate, false);
  }

  get driverAppliedFuelSurchargeType(): string {
    if (
      !this.fuelLevyType ||
      this.job.accounting.fleetAssetRates.length === 0
    ) {
      return '';
    }
    const rate: RateTableItems = this.job.accounting.fleetAssetRates[0].rate;
    const zeroPercentClientRate: boolean =
      this.job.accounting.additionalCharges.clientFuelSurcharge &&
      this.job.accounting.additionalCharges.clientFuelSurcharge
        .fuelSurchargeRate === 0
        ? true
        : false;
    return this.getAppliedFuelSurchargeTypeDisplayName(
      rate,
      zeroPercentClientRate,
    );
  }

  // returns the total quanity (time) that demurrage was applied to
  get demurrageQuantity(): ClientAndFleetAsset {
    const clientDemurrageTimes: number[] =
      this.finishedJobData.clientDemurrageBreakdown.map(
        (x: DemurrageRateData) => x.demurrageDurationInMilliseconds,
      );
    const fleetAssetDemurrageTimes: number[] =
      this.finishedJobData.fleetAssetDemurrageBreakdown.map(
        (x: DemurrageRateData) => x.demurrageDurationInMilliseconds,
      );

    const clientTotalDurationInMilliseconds = clientDemurrageTimes.reduce(
      (accumulator, curr) => accumulator + curr,
    );
    const fleetAssetDurationInMilliseconds = fleetAssetDemurrageTimes.reduce(
      (accumulator, curr) => accumulator + curr,
    );

    return {
      client:
        moment.duration(clientTotalDurationInMilliseconds).hours() +
        'h ' +
        moment.duration(clientTotalDurationInMilliseconds).minutes() +
        'm',
      fleetAsset:
        moment.duration(fleetAssetDurationInMilliseconds).hours() +
        'h ' +
        moment.duration(fleetAssetDurationInMilliseconds).minutes() +
        'm',
    };
  }

  get demurrageRate(): ClientAndFleetAsset {
    const clientDemurrageRates: number[] =
      this.finishedJobData.clientDemurrageBreakdown.map(
        (x: DemurrageRateData) => x.rate,
      );
    const fleetAssetDemurrageRates: number[] =
      this.finishedJobData.fleetAssetDemurrageBreakdown.map(
        (x: DemurrageRateData) => x.rate,
      );

    const clientIsVariousRates = !clientDemurrageRates.every(
      (val, i, arr) => val === arr[0],
    );
    const fleetAssetIsVariousRates = !fleetAssetDemurrageRates.every(
      (val, i, arr) => val === arr[0],
    );

    const clientRateTypeId = (this.chargeItem as JobPrimaryRate).rate
      .rateTypeId;
    const fleetAssetRateTypeId = (this.secondaryChargeItem as JobPrimaryRate)
      .rate.rateTypeId;
    let clientRate = 0;
    let fleetAssetRate = 0;

    if (!clientIsVariousRates && clientDemurrageRates.length > 0) {
      if (clientRateTypeId === 4) {
        clientRate = (
          (this.chargeItem as JobPrimaryRate).rate
            .rateTypeObject as PointToPointRateType
        ).demurrage.rate;
      } else {
        clientRate = clientDemurrageRates[0];
      }
    }

    if (!fleetAssetIsVariousRates && fleetAssetDemurrageRates.length > 0) {
      if (fleetAssetRateTypeId === 4) {
        fleetAssetRate = (
          (this.secondaryChargeItem as JobPrimaryRate).rate
            .rateTypeObject as PointToPointRateType
        ).demurrage.rate;
      } else {
        fleetAssetRate = fleetAssetDemurrageRates[0];
      }
    }

    return {
      client: clientIsVariousRates
        ? 'Various'
        : '$' + this.displayCurrencyValue(clientRate) + '/ph',
      fleetAsset: fleetAssetIsVariousRates
        ? 'Various'
        : '$' + this.displayCurrencyValue(fleetAssetRate) + '/ph',
    };
  }

  public getAppliedFuelSurchargeTypeDisplayName(
    rate: RateTableItems,
    zeroPercentClientRate: boolean,
  ): string {
    let displayName = 'Various';
    let appliedFuelSurchargeId: number = -1;
    switch (rate.rateTypeId) {
      case 1:
        appliedFuelSurchargeId = (rate.rateTypeObject as TimeRateType)
          .appliedFuelSurchargeId;
        break;
      case 6:
        appliedFuelSurchargeId = rate.fuelSurcharge ? 1 : 3;
        break;
    }

    const appliedFuelSurcharge = this.applicableFuelSurcharges.find(
      (x: ShortLongName) => x.id === appliedFuelSurchargeId,
    );

    if (!appliedFuelSurcharge) {
      return displayName;
    }

    switch (appliedFuelSurchargeId) {
      case 1:
        displayName = 'Fuel Applied';
        break;
      case 2:
        if (zeroPercentClientRate) {
          // tslint:disable-next-line
          displayName = "Don't Apply";
        } else {
          displayName = 'Fuel Applied';
        }

        break;
      case 3:
        // tslint:disable-next-line
        displayName = "Don't Apply";
        break;
    }
    return displayName;
  }

  get fleetAssetUnitFreightQuantity(): string {
    return (
      '$' +
      this.displayCurrencyValue(
        this.job.accounting.totals.subtotals.freightCharges.client,
      )
    );
  }

  get clientFuelSurchargeQuantity(): number {
    if (!this.fuelLevyType || this.job.accounting.clientRates.length === 0) {
      return 0;
    }
    const rate: RateTableItems = this.job.accounting.clientRates[0].rate;

    let freight = this.job.accounting.totals.subtotals.freightCharges.client;

    switch (rate.rateTypeId) {
      case 2:
        freight = 0;
        const zoneRateData = this.job.accounting.finishedJobData
          .clientRateData as ZoneRateData[];
        for (let i = 0; i < zoneRateData.length - 1; i++) {
          const zone: ZoneRateType | undefined = (
            this.job.accounting.clientRates[0].rate
              .rateTypeObject as ZoneRateType[]
          ).find((z: ZoneRateType) => zoneRateData[i].zoneId === z.zone);
          if (zone && zone.appliedFuelSurchargeId === 1) {
            freight += zone.rate;
          }
        }
        break;
      case 5:
        freight = 0;
        const unitRateData: ZonedUnitRateData[] = (
          this.job.accounting.finishedJobData.clientRateData as UnitRateData
        ).zonedUnitRateData;
        for (let i = 0; i < unitRateData.length; i++) {
          const unit: UnitRate | undefined = (
            this.job.accounting.clientRates[0].rate.rateTypeObject as UnitRate[]
          ).find((u: UnitRate) => unitRateData[i].zoneId === u.zoneId);
          if (unit && unit.appliedFuelSurchargeId === 1) {
            freight += unitRateData[i].freightChargesTotalExclGst;
            if (unit.fuelIsAppliedToFlagFalls) {
              freight += unitRateData[i].loadChargesTotalExclGst;
            }
          }
        }
        break;
      case 4:
        freight = 0;
        const pointToPointRate: PointToPointRateType = this.job.accounting
          .clientRates[0].rate.rateTypeObject as PointToPointRateType;
        freight =
          pointToPointRate.appliedFuelSurchargeId === 1
            ? this.job.accounting.totals.subtotals.freightCharges.client
            : 0;
        break;
    }

    const outsideMetro =
      this.job.accounting.totals.subtotals.outsideMetroChargeTotals.client;
    const standby =
      this.job.accounting.totals.subtotals.standbyChargeTotals.client;
    const freightAdjustment: number =
      this.job.accounting.totals.subtotals.freightAdjustmentCharges.client;
    const clientDemurrageRates =
      this.job.accounting.finishedJobData.clientDemurrageBreakdown;
    const clientDemurrageFuelSurcharges =
      this.job.accounting.finishedJobData.clientDemurrageFuelSurchargeBreakdown;
    let demurrageCharge: number = 0;
    for (const demurrage of clientDemurrageRates) {
      const demurrageFuelBreakdown = clientDemurrageFuelSurcharges.find(
        (x: FuelSurchargeBreakdown) => x.pudId === demurrage.pudId,
      );
      if (!demurrageFuelBreakdown) {
        continue;
      }
      if (
        demurrage.demurrageFuelSurchargeApplies &&
        demurrageFuelBreakdown.fuelSurchargeRate > 0 &&
        demurrage.demurrageChargeExclGst > 0
      ) {
        demurrageCharge += demurrage.demurrageChargeExclGst;
      }
    }
    return this.getStandbyFuelSurchargeQuantity(
      rate,
      freight,
      outsideMetro,
      standby,
      demurrageCharge,
      freightAdjustment,
    );
  }

  get driverStandbyFuelSurchargeQuantity(): number {
    if (
      !this.fuelLevyType ||
      this.job.accounting.fleetAssetRates.length === 0
    ) {
      return 0;
    }
    const rate: RateTableItems = this.job.accounting.fleetAssetRates[0].rate;

    let freight =
      this.job.accounting.totals.subtotals.freightCharges.fleetAsset;

    const clientFuelSurchargeRate = (this.chargeItem as ClientFuelSurchargeRate)
      .fuelSurchargeRate;
    switch (rate.rateTypeId) {
      case 2:
        freight = 0;
        const zoneRateData = this.job.accounting.finishedJobData
          .fleetAssetRateData as ZoneRateData[];
        for (let i = 0; i < zoneRateData.length; i++) {
          const zone: ZoneRateType = (
            this.job.accounting.fleetAssetRates[0].rate
              .rateTypeObject as ZoneRateType[]
          )[0];
          if (
            zone &&
            (zone.appliedFuelSurchargeId === 1 ||
              (zone.appliedFuelSurchargeId === 2 &&
                clientFuelSurchargeRate > 0))
          ) {
            freight +=
              this.job.accounting.totals.subtotals.freightCharges.fleetAsset;
          }
        }
        break;
      case 5:
        freight = 0;
        const unit: UnitRate | undefined = (
          this.job.accounting.fleetAssetRates[0].rate
            .rateTypeObject as UnitRate[]
        )[0];
        if (
          unit &&
          (unit.appliedFuelSurchargeId === 1 ||
            (unit.appliedFuelSurchargeId === 2 && clientFuelSurchargeRate > 0))
        ) {
          freight +=
            this.job.accounting.totals.subtotals.freightCharges.fleetAsset;
        }
        break;
      case 4:
        freight = 0;
        const pointToPointRate: PointToPointRateType = this.job.accounting
          .fleetAssetRates[0].rate.rateTypeObject as PointToPointRateType;
        freight =
          pointToPointRate.appliedFuelSurchargeId === 1 ||
          (pointToPointRate.appliedFuelSurchargeId === 2 &&
            clientFuelSurchargeRate > 0)
            ? this.job.accounting.totals.subtotals.freightCharges.fleetAsset
            : 0;
        break;
    }

    const outsideMetro =
      this.job.accounting.totals.subtotals.outsideMetroChargeTotals.fleetAsset;
    const standby =
      this.job.accounting.totals.subtotals.standbyChargeTotals.fleetAsset;

    const freightAdjustment: number =
      this.job.accounting.totals.subtotals.freightAdjustmentCharges.fleetAsset;

    const driverDemurrageRates =
      this.job.accounting.finishedJobData.fleetAssetDemurrageBreakdown;
    const driverDemurrageFuelSurcharges =
      this.job.accounting.finishedJobData
        .fleetAssetDemurrageFuelSurchargeBreakdown;

    let demurrageCharge: number = 0;

    for (const demurrage of driverDemurrageRates) {
      const demurrageFuelBreakdown = driverDemurrageFuelSurcharges.find(
        (x: FuelSurchargeBreakdown) => x.pudId === demurrage.pudId,
      );

      if (!demurrageFuelBreakdown) {
        continue;
      }
      if (
        demurrage.demurrageFuelSurchargeApplies &&
        demurrageFuelBreakdown.fuelSurchargeRate > 0 &&
        demurrage.demurrageChargeExclGst > 0
      ) {
        demurrageCharge += demurrage.demurrageChargeExclGst;
      }
    }

    return this.getStandbyFuelSurchargeQuantity(
      rate,
      freight,
      outsideMetro,
      standby,
      demurrageCharge,
      freightAdjustment,
    );
  }

  // returns the total charge that fuel surcharge is applied against.
  public getStandbyFuelSurchargeQuantity(
    rate: RateTableItems,
    freightCharge: number,
    outsideMetroCharge: number,
    strandbyCharge: number,
    demurrageCharge: number,
    freightAdjustment: number,
  ): number {
    let quantity: number = this.roundCurrencyValue(
      freightCharge + outsideMetroCharge + demurrageCharge + freightAdjustment,
    );

    switch (rate.rateTypeId) {
      case 1:
        const standbyFuelSurchargeApplies = (
          rate.rateTypeObject as TimeRateType
        ).standbyFuelSurchargeApplies;
        if (standbyFuelSurchargeApplies) {
          quantity = this.roundCurrencyValue(
            freightCharge +
              outsideMetroCharge +
              strandbyCharge +
              freightAdjustment,
          );
        }
        break;
    }
    return quantity;
  }

  public returnMatchingValueCheck(quantity: boolean = false): boolean {
    if (!quantity) {
      if (this.freightChargeType) {
        if (
          this.chargeItem instanceof JobPrimaryRate &&
          this.chargeItem.rate.rateTypeId ===
            this.secondaryChargeItem.rate.rateTypeId
        ) {
          return true;
        } else {
          return false;
        }
      } else if (this.additionalChargeType) {
        if (this.chargeItem instanceof AdditionalChargeItem) {
          return true;
        } else {
          return false;
        }
      } else if (this.fuelLevyType) {
        if (this.chargeItem instanceof ClientFuelSurchargeRate) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      if (this.freightChargeType) {
        return true;
      } else {
        return false;
      }
    }
  }

  get freightRowSpan() {
    let rowSpan = 3;

    if (this.freightChargeType) {
      if (
        this.outsideMetroCharge &&
        this.outsideMetroCharge.isOutsideMetroChargeDriver
      ) {
        rowSpan++;
      }
      if (
        this.outsideMetroCharge &&
        this.outsideMetroCharge.isOutsideMetroChargeClient
      ) {
        rowSpan++;
      }

      if (this.isEquipmentHire) {
        rowSpan++;
      }
    }

    return rowSpan;
  }

  get clientStandbyRate() {
    const foundMultiplier = rateMultipliers.find(
      (x: RateMultipliers) => x.id === this.clientRate.standbyMultiplier,
    );
    if (!foundMultiplier) {
      return '0.00';
    }

    if (!this.isStandbyRate) {
      return '0.00';
    }

    if (foundMultiplier) {
      let rateString = '';

      rateString +=
        '$' +
        this.displayCurrencyValue(this.clientRate.standbyRate) +
        (foundMultiplier.longName.toLowerCase() === 'hour'
          ? '/ph'
          : ' per ' + foundMultiplier.longName.toLowerCase());
      return rateString;
    }
  }

  get fleetAssetStandbyRate() {
    const foundMultiplier = rateMultipliers.find(
      (x: RateMultipliers) => x.id === this.fleetAssetRate.standbyMultiplier,
    );
    if (!this.isStandbyRate) {
      return '0.00';
    }
    if (!foundMultiplier) {
      return '0.00';
    }

    if (foundMultiplier) {
      let rateString = '';

      rateString +=
        '$' +
        this.displayCurrencyValue(this.fleetAssetRate.standbyRate) +
        (foundMultiplier.longName.toLowerCase() === 'hour'
          ? '/ph'
          : ' per ' + foundMultiplier.longName.toLowerCase());
      return rateString;
    }
  }

  /**
   * Used in column 4 for the client freight quantity. This will display the quantity
   * for the client based on the rate type.
   */
  get clientFreightQuantity(): string {
    if (!this.job.accounting.finishedJobData) {
      return '';
    }

    if (
      this.job.accounting?.clientRates?.[0]?.rate.rateTypeId ===
      JobRateType.UNIT
    ) {
      const unitRateData = this.job.accounting.finishedJobData
        .clientRateData as UnitRateData;
      const unitCount = unitRateData.zonedUnitRateData.reduce(
        (count: number, currentValue: ZonedUnitRateData) => {
          return count + currentValue.quantityOfUnits;
        },
        0,
      );
      return `${unitCount}`;
    } else if (
      isDistanceRateData(
        this.job.accounting?.clientRates?.[0]?.rate.rateTypeId,
        this.job.accounting.finishedJobData.clientRateData,
      )
    ) {
      return `${this.job.accounting.finishedJobData.clientRateData.calculatedTotalDistance}km`;
    }

    return '0';
  }

  /**
   * Used in column 5 for the freight rate. This will display the rate for the client
   * based on the rate type.
   */
  get clientFreightRate(): string {
    if (!this.freightChargeType) {
      return '';
    }
    try {
      const jobPrimaryRate = this.chargeItem as JobPrimaryRate;
      const finishedJobData = this.job.accounting.finishedJobData;
      const variancePct =
        this.job.accounting.clientServiceRateVariations
          ?.clientAdjustmentPercentage ?? 0;

      switch (jobPrimaryRate.rate.rateTypeId) {
        case JobRateType.UNIT:
        case JobRateType.ZONE:
          return 'Various;';
        case JobRateType.DISTANCE:
          if (
            isDistanceRateData(
              jobPrimaryRate.rate.rateTypeId,
              finishedJobData.clientRateData,
            ) &&
            isDistanceRateTypeObject(
              jobPrimaryRate.rate.rateTypeId,
              jobPrimaryRate.rate.rateTypeObject,
            )
          ) {
            if (finishedJobData.clientRateData.rangeSubtotals.length === 1) {
              const rangeSubtotal =
                finishedJobData.clientRateData.rangeSubtotals[0];
              const foundRate = jobPrimaryRate.rate.rateTypeObject.rates.find(
                (x: RangedRate) => x.id === rangeSubtotal.rangeRateId,
              );
              if (!foundRate) {
                return '';
              }
              const variation = getPercentageOf(foundRate.rate, variancePct);
              return `$${DisplayCurrencyValue(foundRate.rate + variation)}/km`;
            } else {
              return 'Various';
            }
          }
          return 'DISTANCE';
        case JobRateType.TIME:
          const baseRate = (jobPrimaryRate.rate.rateTypeObject as TimeRateType)
            .rate;
          const variation = getPercentageOf(baseRate, variancePct);
          return `$${DisplayCurrencyValue(baseRate + variation)}`;
        case JobRateType.TRIP:
          return `$${DisplayCurrencyValue(
            (jobPrimaryRate.rate.rateTypeObject as TripRate).rate,
          )}`;
        default:
          return '';
      }
    } catch (error) {
      logConsoleError('Something went wrong displaying client rate', error);
      return 'N/A';
    }
  }

  /**
   * Used in column 4 for the fleet asset quantity. This will display the quantity
   * for the fleet asset based on the rate type.
   */
  get fleetAssetFreightQuantity(): string {
    if (!this.job.accounting.finishedJobData) {
      return '';
    }
    // TODO - combine other rate types into this getter
    if (
      isDistanceRateData(
        this.job.accounting?.fleetAssetRates?.[0]?.rate.rateTypeId,
        this.job.accounting.finishedJobData.fleetAssetRateData,
      )
    ) {
      return `${this.job.accounting.finishedJobData.fleetAssetRateData.calculatedTotalDistance}km`;
    }

    return '0';
  }

  /**
   * Used in column 5 for the freight rate. This will display the rate for the
   * fleet asset based on the rate type.
   */
  get fleetAssetFreightRate(): string {
    if (!this.freightChargeType) {
      return '';
    }
    try {
      const jobPrimaryRate = this.secondaryChargeItem as JobPrimaryRate;
      const finishedJobData = this.job.accounting.finishedJobData;
      const variancePct =
        this.job.accounting.clientServiceRateVariations
          ?.fleetAssetAdjustmentPercentage ?? 0;

      switch (jobPrimaryRate.rate.rateTypeId) {
        case JobRateType.UNIT:
          return `${
            (this.secondaryChargeItem.rate.rateTypeObject as UnitRate[])[0]
              .fleetAssetPercentage
          }%`;
        case JobRateType.ZONE:
          return `${
            (this.secondaryChargeItem.rate.rateTypeObject as ZoneRateType[])[0]
              .percentage
          }%`;
        case JobRateType.DISTANCE:
          if (
            isDistanceRateData(
              jobPrimaryRate.rate.rateTypeId,
              finishedJobData.fleetAssetRateData,
            ) &&
            isDistanceRateTypeObject(
              jobPrimaryRate.rate.rateTypeId,
              jobPrimaryRate.rate.rateTypeObject,
            )
          ) {
            if (
              finishedJobData.fleetAssetRateData.rangeSubtotals.length === 1
            ) {
              const rangeSubtotal =
                finishedJobData.fleetAssetRateData.rangeSubtotals[0];
              const foundRate = jobPrimaryRate.rate.rateTypeObject.rates.find(
                (x: RangedRate) => x.id === rangeSubtotal.rangeRateId,
              );
              return foundRate
                ? `$${DisplayCurrencyValue(foundRate.rate)}/km`
                : '';
            } else {
              return 'Various';
            }
          }
          return 'DISTANCE';
        case JobRateType.TIME:
          const baseRate = (jobPrimaryRate.rate.rateTypeObject as TimeRateType)
            .rate;
          const variation = getPercentageOf(baseRate, variancePct);
          return `$${DisplayCurrencyValue(baseRate + variation)}`;
        case JobRateType.TRIP:
          return `$${DisplayCurrencyValue(
            (jobPrimaryRate.rate.rateTypeObject as TripRate).rate,
          )}`;
        default:
          return '';
      }
    } catch (error) {
      logConsoleError(
        'Something went wrong displaying fleet asset rate',
        error,
      );
      return 'N/A';
    }
  }

  get totals() {
    return this.job.accounting.totals;
  }

  get equipmentHireQuantity() {
    if (this.isEquipmentHire) {
      return this.totals.subtotals.equipmentHireCharges.length;
    }
  }

  get equipmentHireRate() {
    if (this.equipmentHireQuantity === 1) {
      const equipment = this.totals.subtotals.equipmentHireCharges[0];
      let chargedAt: string = '';
      chargedAt += equipment.contractJobRate.jobRate + ' per ';
      const foundMultiplier = this.rateMultipliers.find(
        (x: RateMultipliers) =>
          x.id === equipment.contractJobRate.jobRateMultiplier,
      );
      if (foundMultiplier) {
        chargedAt += foundMultiplier.longName;
      }
      return chargedAt;
    }
  }

  get freightMargin() {
    if (
      (this.currentClientRate || this.currentClientRate === 0) &&
      (this.currentFleetAssetRate || this.currentFleetAssetRate === 0)
    ) {
      if (this.freightChargeType) {
        if (!this.outsideMetroType) {
          const clientFreightTotal =
            this.totals.subtotals.freightCharges.client;
          const fleetFreightTotal =
            this.totals.subtotals.freightCharges.fleetAsset;
          return new JobAccountingMargin(
            RoundCurrencyValue(clientFreightTotal - fleetFreightTotal),
            RoundCurrencyValue(
              ((clientFreightTotal - fleetFreightTotal) / clientFreightTotal) *
                100,
            ),
          );
        } else {
          const clientOutsideMetroTotal = this.outsideMetroCharge.clientAmount;
          const fleetOutsideMetroTotal = this.outsideMetroCharge.driverAmount;

          return new JobAccountingMargin(
            RoundCurrencyValue(
              clientOutsideMetroTotal - fleetOutsideMetroTotal,
            ),
            RoundCurrencyValue(
              ((clientOutsideMetroTotal - fleetOutsideMetroTotal) /
                clientOutsideMetroTotal) *
                100,
            ),
          );
        }
      } else {
        const clientFreight = this.currentClientRate;
        const fleetAssetFreight = this.currentFleetAssetRate;
        const overallMargin = new JobAccountingMargin(
          RoundCurrencyValue(clientFreight - fleetAssetFreight),
          RoundCurrencyValue(
            ((clientFreight - fleetAssetFreight) / clientFreight) * 100,
          ),
        );
        return overallMargin;
      }
    }
    return new JobAccountingMargin(0, 0);
  }

  get additionalChargeItemList() {
    return useRootStore().additionalChargeItemList;
  }

  get tollAdminAndHandlingId(): string | undefined {
    return useRootStore().tollAdminAndHandlingId;
  }

  get currentClientRate(): number {
    if (this.additionalChargeType) {
      const chargeItem: AdditionalChargeItem = this
        .chargeItem as AdditionalChargeItem;

      // We should look up the associated $ value in the subtotals to deal with
      // differences from percentage chargeBasis or quantities
      const chargeSubtotal: AdditionalChargeSubtotal | undefined =
        this.job.accounting.totals.subtotals.additionalChargeItems.find(
          (x: AdditionalChargeSubtotal) =>
            x.chargeRef === chargeItem.typeReferenceId,
        );
      if (!chargeSubtotal) {
        logConsoleError(
          'Could not find charge subtotal for Additional Charge Item',
          chargeItem,
        );
        return 0;
      }
      return (
        chargeSubtotal.items.find(
          (x: AdditionalChargeSubtotal) => x.chargeRef === chargeItem._id,
        )?.total.client ?? chargeItem.client.charge
      );
    }
    if (this.fuelLevyType) {
      return this.totals.subtotals.fuelSurcharges.client;
    }
    if (this.freightChargeType) {
      return this.totals.subtotals.freightCharges.client;
    }
    if (this.isStandbyRate) {
      return this.totals.subtotals.standbyChargeTotals.client;
    }
    if (this.isDemurrageRate) {
      return this.totals.subtotals.demurrageChargeTotals.client;
    }
    return 0;
  }

  get currentFleetAssetRate() {
    if (this.additionalChargeType) {
      const chargeItem: AdditionalChargeItem = this
        .chargeItem as AdditionalChargeItem;

      // We should look up the associated $ value in the subtotals to deal with
      // differences from percentage chargeBasis or quantities
      const chargeSubtotal: AdditionalChargeSubtotal | undefined =
        this.job.accounting.totals.subtotals.additionalChargeItems.find(
          (x: AdditionalChargeSubtotal) =>
            x.chargeRef === chargeItem.typeReferenceId,
        );
      if (!chargeSubtotal) {
        logConsoleError(
          'Could not find charge subtotal for Additional Charge Item',
          chargeItem,
        );
        return 0;
      }
      const result =
        chargeSubtotal.items.find(
          (x: AdditionalChargeSubtotal) => x.chargeRef === chargeItem._id,
        )?.total.fleetAsset ?? chargeItem.fleetAsset.charge;
      return result;
    }
    if (this.fuelLevyType) {
      return this.totals.subtotals.fuelSurcharges.fleetAsset;
    }
    if (this.freightChargeType) {
      return this.totals.subtotals.freightCharges.fleetAsset;
    }
    if (this.isStandbyRate) {
      return this.totals.subtotals.standbyChargeTotals.fleetAsset;
    }
    if (this.isDemurrageRate) {
      return this.totals.subtotals.demurrageChargeTotals.fleetAsset;
    }
    return 0;
  }

  public hasGstApplied(fleetAsset: boolean = false) {
    return !fleetAsset
      ? this.clientGstRegistered
      : this.fleetAssetGstRegistered;
  }

  public returnRateString(value: number) {
    const isPositive = Math.sign(value) >= 0;

    if (isPositive) {
      return '$' + this.displayCurrencyValue(Math.abs(value));
    } else {
      return '- $' + this.displayCurrencyValue(Math.abs(value));
    }
  }
}

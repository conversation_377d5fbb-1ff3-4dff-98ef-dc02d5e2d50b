import DriverChatHistory from '@/components/common/driver_chat_history/driver_chat_history.vue';
import DriverConversation from '@/components/common/driver_conversation/index.vue';
import DriverDeviceSnapshotSummary from '@/components/common/driver_device_snapshot_summary.vue';
import DriverMessageComponent from '@/components/common/driver_message_component/index.vue';
import JobAttachmentApproval from '@/components/common/job_attachment_approval/index.vue';
import JobStatusManagement from '@/components/common/job_status_management/job_status_management.vue';
import NotesEditor from '@/components/common/notes_editor/notes_editor.vue';
import NotesList from '@/components/common/notes_list/notes_list.vue';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';
import JobContentDialog from '@/components/common/ui-elements/job_content_dialog.vue';
import UnassignedPudLinkingMaintenance from '@/components/common/unassigned_pud_linking_maintenance/index.vue';
import CashSalesDetails from '@/components/operations/BookJob/cash_sales_details/index.vue';
import FinaliseJobAccountingTable from '@/components/operations/FinaliseJob/finalise_job_accounting_table.vue';
import JobDetailsInformation from '@/components/operations/OperationDashboard/components/JobDetails/index.vue';
import AdjustServiceDialogsDialog from '@/components/operations/OperationDashboard/components/adjust_service_details_dialog.vue';
import AssetManagement from '@/components/operations/OperationDashboard/components/JobDetailsDialog/asset_management/asset_management.vue';
import JobDetailsEventListSummary from '@/components/operations/OperationDashboard/components/JobDetailsDialog/job_details_event_list_summary/index.vue';
import RebookJobDialog from '@/components/operations/OperationDashboard/components/JobDetailsDialog/rebook_job_dialog/rebook_job_dialog.vue';
import JobPricingManagement from '@/components/operations/ReviewJob/job_pricing_management/index.vue';
import JobRequirementManagement from '@/components/operations/ReviewJob/job_requirement_management.vue';
import {
  JobRouteMapConfig,
  RouteViewType,
} from '@/components/operations/maps/job_map_route/JobRouteMapConfig';
import JobMapRoute from '@/components/operations/maps/job_map_route/index.vue';
import { RoundCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  returnDurationFromMilliseconds,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  distanceFromPoints,
  validateGpsData,
} from '@/helpers/DistanceHelpers/DistanceHelpers';
import { allAdditionalAssetsValidForPricing } from '@/helpers/HireContractHelpers';
import {
  bookPreloadFollowOnJob,
  referenceListContainsPreloadReference,
  returnPreloadPrefix,
} from '@/helpers/JobBooking/JobBookingPreloadHelpers';
import { checkExistingPudReferences } from '@/helpers/JobDataHelpers/JobDataHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import {
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { generateAccountingFinishedJobData } from '@/helpers/RateHelpers/RateHelpers';
import { requestJobDistanceFromGps } from '@/helpers/RouteHelpers/JobRouteHelpers';
import {
  hasAdminOrTeamLeaderOrBranchManagerRole,
  hasAdminRole,
} from '@/helpers/UserRoleHelpers/UserRoleHelpers';
import {
  initialiseJobAccountingDetails,
  initialiseJobDetails,
} from '@/helpers/classInitialisers/InitialiseJobDetails';
import { initialisePudItem } from '@/helpers/classInitialisers/InitialisePudItem';
import {
  APIListUsernamesForClientRequest,
  APIListUsernamesForClientResponse,
} from '@/interface-models/Client/APIListUsernamesForClientResponse';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import ClientReferenceDetails from '@/interface-models/Client/ClientDetails/References/ClientReferenceDetails';
import { DeviceQueryType } from '@/interface-models/Driver/Device/DeviceQueryType';
import { DriverDeviceQuery } from '@/interface-models/Driver/Device/DriverDeviceQuery';
import { DriverDeviceSnapshot } from '@/interface-models/Driver/Device/DriverDeviceSnapshot';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import {
  AdditionalAccountingData,
  returnAdditionalAccountingData,
} from '@/interface-models/Generic/Accounting/AdditionalAccountingData';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import { JobAccountingTotals } from '@/interface-models/Generic/Accounting/JobAccountingTotals/JobAccountingTotals';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { KeyValuePair } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import GpsMarkerDetails from '@/interface-models/Generic/Position/GpsMarkerDetails';
import GpsPosition from '@/interface-models/Generic/Position/GpsPosition';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import AdditionalAsset from '@/interface-models/Jobs/AdditionalAsset';
import { JobEventType } from '@/interface-models/Jobs/Event/JobEventType';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import { FinishedJobDetails } from '@/interface-models/Jobs/FinishedJobDetails/FinishedJobDetails';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import JobDistanceTravelled from '@/interface-models/Jobs/JobDistanceTravelled';
import { JobEventSummary } from '@/interface-models/Jobs/JobEventSummary';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { JobSourceType } from '@/interface-models/Jobs/JobSourceType';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { IUserAuthority } from '@/interface-models/Roles/UserAuthorityInterface';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  components: {
    NotesEditor,
    JobContentDialog,
    JobPricingManagement,
    NotesList,
    JobMapRoute,
    JobStatusManagement,
    JobDetailsInformation,
    JobRequirementManagement,
    JobAttachmentApproval,
    FinaliseJobAccountingTable,
    DriverMessageComponent,
    JobDetailsEventListSummary,
    RebookJobDialog,
    AdjustServiceDialogsDialog,
    ConfirmationDialog,
    AssetManagement,
    CashSalesDetails,
    UnassignedPudLinkingMaintenance,
    DriverChatHistory,
    ContentDialog,
    DriverDeviceSnapshotSummary,
    DriverConversation,
  },
})
export default class JobDetailsDialog extends Vue implements IUserAuthority {
  @Prop() public jobDetails: JobDetails;
  @Prop() public showJobDetailsDialog: boolean;
  @Prop({ default: false }) public isJobSearchScreen: boolean;
  public clientDetails: ClientDetails = new ClientDetails();

  public jobStore = useJobStore();
  public fleetAssetStore = useFleetAssetStore();
  public dataImportStore = useDataImportStore();
  public operationsStore = useOperationsStore();
  public clientDetailsStore = useClientDetailsStore();

  public isLoading: boolean = true;

  // Job Pricing
  public jobGpsData: GpsPosition[] = [];
  public appliedHireContracts: HireContract[] = [];
  public accountingTotalsComputed: boolean = false;
  public jobAccountingDetails: JobAccountingDetails =
    new JobAccountingDetails();
  public clientMinimumDurationMet: boolean = false;
  public driverMinimumDurationMet: boolean = false;

  public isRequestingJobGpsData: boolean = false;

  public isViewingDriverDeviceInfoDialog: boolean = false;
  public isViewingDeviceRequestDialog: boolean = false;
  public driverDeviceSnapshot: DriverDeviceSnapshot | null = null;

  public pudItems: PUDItem[] = [];
  public newJobNotes: Communication[] = [];

  public isEditingForm: boolean = false;
  public awaitingJobReferencesSaveRequest: boolean = false;
  public jobReferences: JobReferenceDetails[] = [];
  public isNewJobReference: boolean = false;
  public distanceTravelled: JobDistanceTravelled = {
    planned: 0,
    gpsEstimate: 0,
  };

  public returnFormattedTime = returnFormattedTime;
  public returnDurationFromMilliseconds = returnDurationFromMilliseconds;
  public showRebookDialog: boolean = false;
  public showServiceDetailsDialog: boolean = false;
  public showUnassignedPudLinkingMaintenance: boolean = false;
  public showMessageHistoryDialog: boolean = false;
  // flag to show integration_requirements component on review
  public showEDIConfirmation: boolean = false;
  public jobRequiresReviewConfirmation: boolean = false;
  public WorkStatus = WorkStatus;

  public awaitingJobUpdateResponse: boolean = false;

  public clientCommonAddressList: ClientCommonAddress[] = [];

  public awaitingSaveResponse: boolean = false;

  public jobMapConfig: JobRouteMapConfig = {
    defaultView: RouteViewType.CAPTURED,
    enableViewToggle: true,
    showCapturedRoute: true,
    showPlannedRoute: true,
    showStopMarkers: true,
    showCapturedRouteSlider: true,
  };

  // 1 not started, 2 in progress, 3 complete
  get menuOptions() {
    return [
      {
        id: 'SUM',
        title: 'Summary',
        hasCompletionIndicator: false,
        isComplete: false,
        isHidden: false,
        isActive: !this.isEditingForm,
      },
      {
        id: 'CSH',
        title: 'Cash Sale Details',
        hasCompletionIndicator: false,
        isComplete: false,
        isHidden: this.jobDetails.client.id !== 'CS',
        isActive: !this.isEditingForm,
      },
      {
        id: 'ASM',
        title: 'Asset Management',
        hasCompletionIndicator: false,
        isComplete: false,
        isHidden: this.isJobSearchScreen,
        isActive: !this.isEditingForm,
      },
      {
        id: 'STM',
        title: 'Status Management',
        hasCompletionIndicator: false,
        isComplete: false,
        isHidden: this.isJobSearchScreen,
        isActive: !this.isEditingForm,
      },
      {
        id: 'EVT',
        title: 'Status History',
        hasCompletionIndicator: false,
        isComplete: false,
        isHidden: false,
        isActive: !this.isEditingForm,
      },
      {
        id: 'MAP',
        title: 'Job Route',
        hasCompletionIndicator: false,
        isComplete: false,
        isHidden: false,
        isActive: !this.isEditingForm,
      },
      // {
      //   id: 'MSG',
      //   title: 'Message Driver',
      //   hasCompletionIndicator: false,
      //   isComplete: false,
      //   isHidden: this.isJobSearchScreen,
      //   isActive:
      //     !this.isEditingForm &&
      //     this.jobDetails.workStatus !== WorkStatus.BOOKED &&
      //     !this.isOutsideHire,
      // },
      {
        id: 'ATT',
        title: 'Attachments',
        hasCompletionIndicator: false,
        isComplete: this.jobDetails.workStatus >= WorkStatus.REVIEWED,
        isHidden: false,
        isActive: !this.isEditingForm,
      },
      {
        id: 'REF',
        title: 'Job Requirements',
        hasCompletionIndicator: true,
        isComplete:
          this.jobReferenceRequirementsMet &&
          this.pudReferenceRequirementsMet &&
          this.jobWeightRequirementsMet,
        isHidden: false,
        isActive: true,
      },
      {
        id: 'PRI',
        title: 'Job Pricing',
        hasCompletionIndicator: true,
        isComplete:
          (this.jobDetails.workStatus >= WorkStatus.REVIEWED &&
            !this.jobDetails.statusList.includes(45)) ||
          (this.accountingTotalsComputed &&
            this.minimumDurationRequirementsMet),
        isHidden: false,
        isActive:
          !this.isEditingForm &&
          ((!this.isJobSearchScreen &&
            !this.pricingScreenActiveRequirements.length &&
            this.jobReferenceRequirementsMet &&
            this.pudReferenceRequirementsMet &&
            this.jobWeightRequirementsMet &&
            !this.isJobSearchScreen &&
            this.jobDetails.workStatus >= WorkStatus.DRIVER_COMPLETED) ||
            (this.isJobSearchScreen &&
              this.jobDetails.workStatus >= WorkStatus.REVIEWED)),
        inactiveDescription: !this.isJobSearchScreen
          ? this.pricingScreenActiveRequirements
          : [],
      },
      {
        id: 'TAB',
        title: 'Accounting Totals',
        hasCompletionIndicator: true,
        isComplete:
          this.jobDetails.workStatus >= WorkStatus.REVIEWED &&
          !this.jobDetails.statusList.includes(45),
        isHidden: false,
        isActive:
          !this.isEditingForm &&
          this.jobDetails.workStatus >= WorkStatus.REVIEWED,
      },
    ];
  }

  /**
   * Returns a boolean indicating whether the job is currently missing a preload
   * job.
   */
  get showPreloadLegButton() {
    if (this.isJobSearchScreen) {
      return false;
    }
    // Check if last leg is a preload dropoff
    const lastPud =
      this.jobDetails.pudItems[this.jobDetails.pudItems.length - 1];
    const lastLegIsPreloadDropoff =
      lastPud.legTypeFlag === 'D' &&
      referenceListContainsPreloadReference(lastPud.dropoffReference);

    // If the last leg is not a preload dropoff, we should not show the button
    if (!lastLegIsPreloadDropoff) {
      return false;
    }
    // Check if there's a note in the job's notes that starts with returnPreloadPrefix()
    const hasPreloadLinkNote = this.jobDetails.notes.some((note) =>
      note.body.startsWith(returnPreloadPrefix()),
    );
    // If the job is currently linked to a preload job, we should not show the
    // button. If the job is not linked to a preload job, we should show the
    // button.
    return !hasPreloadLinkNote;
  }

  /**
   * Calls from template when selecting the 'Book Follow-on Job' menu option
   * (available only on jobs with final preload dropoff). Creates and saves a
   * follow-on preload job and closes the dialog.
   */
  public async bookPreloadJob() {
    this.awaitingSaveResponse = true;
    await bookPreloadFollowOnJob(this.jobDetails);
    this.awaitingSaveResponse = false;
    this.showDialog = false;
  }

  public setRebookDialog() {
    this.showRebookDialog = !this.showRebookDialog;
  }

  public setShowServiceDetailsDialog() {
    // If the job requires review we should show the requires review dialog and restrict access to service rate adjustments.
    if (this.jobDetails.statusList.includes(57)) {
      this.jobRequiresReviewConfirmation = true;
      return;
    }
    this.showServiceDetailsDialog = !this.showServiceDetailsDialog;
  }
  // closes job requires review dialog
  public confirmJobRequiresReview() {
    this.jobRequiresReviewConfirmation = false;
  }

  // Data List to be displayed in the top left of
  get summaryInfoList() {
    const infoList: KeyValuePair[] = [];
    infoList.push({
      id: 'client',
      title: 'Client',
      value: this.jobDetails.client ? this.jobDetails.client.clientName : '-',
    });

    if (this.jobDetails.additionalJobData) {
      const ajd = this.jobDetails.additionalJobData;
      if (this.jobDetails.workStatus >= WorkStatus.ALLOCATED) {
        infoList.push({
          id: 'driver',
          title: 'Driver',
          value: `${ajd.csrAssignedId} - ${ajd.driverName}`,
        });
        infoList.push({
          id: 'driver-number',
          title: 'Driver Contact',
          value: ajd.driverContactNumber,
        });
        infoList.push({
          id: 'fleet-rego',
          title: 'Truck Rego',
          value: ajd.vehicleRegistrationNumber,
        });
      }
    }

    return infoList;
  }

  get equipmentHireContracts() {
    return this.fleetAssetStore.hireContracts;
  }

  get weightRequirement() {
    return this.clientDetails.weightRequirement;
  }

  // ===========================================================================
  // Navigation and View
  // ===========================================================================

  get selectedViewType() {
    const selectedTabId = this.operationsStore.selectedJobDetailsDialogTab;
    return selectedTabId ? selectedTabId : 'SUM';
  }
  set selectedViewType(tabId: string) {
    this.requestMissingDataForViewType(tabId);
    this.operationsStore.setSelectedJobDetailsDialogTab(tabId);
  }

  /**
   * If we're changing navigation tab, requests any missing required data for the new view.
   * @param tabId the tab we're navigating to
   */
  public async requestMissingDataForViewType(tabId: string) {
    // If we're on the map or pricing screens, we need some route information
    if (tabId === 'MAP' || tabId === 'PRI') {
      // If we don't have the plannedRoute, we should request it
      if (!this.jobDetails.plannedRoute) {
        this.setPlannedDistanceFromRoute(
          await this.jobDetails.requestCurrentRouteData(),
        );
      } else {
        // If we have the route, set the local variable for distance
        this.setPlannedDistanceFromRoute(this.jobDetails.plannedRoute);
      }
      // If we're on the map tab and we don't have GPS data, request it
      if (tabId === 'MAP' && (!this.jobGpsData || !this.jobGpsData.length)) {
        this.requestJobGpsData();
      }
      // If we're on the pricing screen and we don't have the distance
      // travelled, request it
      if (
        tabId === 'PRI' &&
        this.distanceTravelled.gpsEstimate === 0 &&
        !this.readOnlyView
      ) {
        this.getDistanceFromGpsData();
      }
    }
  }

  /**
   * Request and response for fetching the distance travelled by a driver over a
   * period of time, as calculated from GPS data.
   */
  public async getDistanceFromGpsData() {
    this.distanceTravelled.gpsEstimate = await requestJobDistanceFromGps(
      this.jobDetails,
    );
  }

  /**
   * Sets the planned distance from the route for the job details. If an error
   * occurs while setting the planned distance, it logs the error and sets the
   * distance to 0.
   */
  public setPlannedDistanceFromRoute(route: ORSRoute | null) {
    if (route === null) {
      this.distanceTravelled.planned = 0;
      return;
    }
    let distanceInKm = 0;
    try {
      const distanceInMetres = route.routes[0].summary.distance;
      distanceInKm = distanceInMetres / 1000;
    } catch (e) {
      console.error('An error occurred while setting planned distance:', e);
      distanceInKm = 0;
    }
    this.distanceTravelled.planned = distanceInKm
      ? RoundCurrencyValue(distanceInKm, 3)
      : 0;
  }

  get showDialog() {
    return this.showJobDetailsDialog;
  }
  set showDialog(value: boolean) {
    this.operationsStore.setViewingJobDetailsDialog(value);
  }

  get readOnlyView() {
    return this.jobDetails.workStatus >= WorkStatus.REVIEWED;
  }

  get hasPendingClientStatus() {
    if (this.jobDetails.client.id === 'CS') {
      return false;
    }
    const clientDetails: ClientSearchSummary | undefined =
      this.clientDetailsStore.clientSummaryList.find(
        (x: ClientSearchSummary) => x.clientId === this.jobDetails.client.id,
      );
    return clientDetails && clientDetails.statusList.includes(3) ? true : false;
  }

  // Set the selected view from the left navigation menu
  public setSelectedView(id: string) {
    if (!id) {
      this.selectedViewType = this.menuOptions[0].id;
      return;
    }

    this.selectedViewType = id;
  }

  get showDialogCloseConfirmation(): boolean {
    return (
      this.isEditingForm ||
      (this.selectedViewType === 'PRI' &&
        this.jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED)
    );
  }

  /**
   * Closes the job details dialog.
   *
   * @param shouldConfirm - Optional parameter indicating whether to show close
   * the dialog by programmatically clicking the close button
   */
  public closeJobDetailsDialog(shouldConfirm: boolean = false) {
    // If we're calling this from handleEscapePress and
    // showDialogCloseConfirmation is true, programmatically press the close
    // button to trigger the confirmation dialog.
    if (shouldConfirm && this.showDialogCloseConfirmation) {
      try {
        const button = document.querySelector(
          '#job-dialog-close-button',
        ) as HTMLElement | null;
        if (button) {
          button.click();
        }
      } catch (e) {
        console.error('Error opening job details dialog confirmation:', e);
      }
      return;
    }
    // Reset the tab in store and close dialog
    this.selectedViewType = 'SUM';
    this.showDialog = false;
  }

  get deviceRequestDialogController() {
    return this.isViewingDeviceRequestDialog;
  }
  set deviceRequestDialogController(value: boolean) {
    this.isViewingDeviceRequestDialog = value;
  }

  get editingReferenceFormController() {
    return this.isEditingForm;
  }
  set editingReferenceFormController(value: boolean) {
    this.setJobRequirementsData(this.jobDetails);
    this.isEditingForm = value;
  }

  /**
   * Sends request to save any reference updates to the job.
   */
  public async saveJobRequirements() {
    this.jobDetails.jobReference = this.jobReferences.filter(
      (r) => !!r.reference,
    );
    this.setUpdatedPudRequirements(this.jobDetails, this.pudItems);
    this.awaitingJobReferencesSaveRequest = true;
    const result = await this.jobStore.updateJobDetails(this.jobDetails);
    if (!result) {
      this.showAppNotification(
        'Failed to save Job References',
        HealthLevel.ERROR,
        'Job Requirements',
      );
    }
    this.editingReferenceFormController = false;
    this.awaitingJobReferencesSaveRequest = false;
  }

  // ===========================================================================
  // Job Weights
  // ===========================================================================

  get jobWeightRequirementsMet() {
    const pudRequirementMetValues: boolean[] = [];
    const firstPickupIndex = this.pudItems.findIndex(
      (pud: PUDItem) => pud.legTypeFlag === 'P',
    );

    this.pudItems.forEach((pud: PUDItem, index: number) => {
      const isFirstPickup: boolean = firstPickupIndex === index;
      const weightIsRequired: boolean = pud.weightIsRequired(
        this.weightRequirement,
        isFirstPickup,
        true,
      );

      const requirementsMet =
        (weightIsRequired &&
          pud.weight !== null &&
          typeof pud.weight !== 'string') ||
        !weightIsRequired;

      pudRequirementMetValues.push(requirementsMet);
    });

    return pudRequirementMetValues.every((v) => v === true);
  }

  // ===========================================================================
  // Job References
  // ===========================================================================

  /**
   * Set the pudItems and jobReferences to the local working variables from the
   * jobDetails object. Called on component mount, and when we enter edit mode
   * on the Job Requirements tab
   */
  public setJobRequirementsData(jobDetails: JobDetails) {
    // Set the pudItems to the local working variable
    this.pudItems = jobDetails.pudItems.map((pud) => initialisePudItem(pud));

    // Set the job references to the local working variable
    if (jobDetails.jobReference) {
      this.jobReferences = deepCopy(jobDetails.jobReference);
    } else {
      this.jobReferences = [new JobReferenceDetails(4, '')];
    }

    this.addMissingJobReferences();
    this.addMissingPudReferences();
  }

  /**
   * Adds missing job references to the jobReferences array.
   * * For each client reference, it checks if a job reference with the same
   *   referenceTypeId already exists in the jobReferences array.
   * * If not, and the client reference's requiredTypeId is 1, 2, or 3, a new
   *   JobReferenceDetails object is created and added to the jobReferences
   *   array.
   * * If the jobReferences array is still empty after processing, a new
   *   JobReferenceDetails object with a referenceTypeId of 4 is added.
   */
  public addMissingJobReferences() {
    for (const clientReference of this.clientDetails.references.mainJobScreen) {
      const referenceExistsInJob = this.jobReferences.find(
        (x: JobReferenceDetails) =>
          x.referenceTypeId === clientReference.referenceTypeId,
      );

      if (referenceExistsInJob || !clientReference.referenceTypeId) {
        continue;
      }

      const referenceRequired =
        clientReference.requiredTypeId === 1 ||
        clientReference.requiredTypeId === 2 ||
        clientReference.requiredTypeId === 3;

      if (referenceRequired) {
        this.jobReferences.push(
          new JobReferenceDetails(clientReference.referenceTypeId, ''),
        );
      }
    }
    // TODO: Assess if we can remove this (if we can safely handle empty reference list in reports and mobile)
    if (this.jobReferences.length < 1) {
      this.jobReferences.push(new JobReferenceDetails(4, ''));
    }
  }

  public addMissingPudReferences() {
    // this.removeUnrequiredPudReferences();
    for (const pud of this.pudItems) {
      const isPickup = pud.legTypeFlag === 'P';
      const pudReferences = isPickup
        ? pud.pickupReference
        : pud.dropoffReference;
      checkExistingPudReferences(
        this.clientDetails.references.pudScreen,
        pudReferences,
        isPickup,
      );
    }
  }

  get pricingScreenActiveRequirements(): string[] {
    const errorMessages: string[] = [];
    if (
      this.jobDetails.workStatus !== WorkStatus.DRIVER_COMPLETED &&
      this.jobDetails.workStatus !== WorkStatus.REVIEWED
    ) {
      errorMessages.push(
        'Job Pricing is only available when the job is Complete.',
      );
    }
    if (!this.jobReferenceRequirementsMet) {
      errorMessages.push(
        'Missing one or more required Job References. Please review in Job Requirements tab.',
      );
    }
    if (!this.pudReferenceRequirementsMet) {
      errorMessages.push(
        'Missing one or more required Pickup/Dropoff References. Please review in Job Requirements tab.',
      );
    }
    if (!this.jobWeightRequirementsMet) {
      errorMessages.push(
        'Missing required payload weight details. Please review in Job Requirements tab.',
      );
    }
    return errorMessages;
  }

  get jobReferenceRequirementsMet(): boolean {
    // Return true if cash sale - no references are required
    if (this.jobDetails.client.id === 'CS') {
      return true;
    }
    // We first check that all required references exist within the job.
    let allReferencesExist: boolean = true;

    for (const clientReference of this.clientDetails.references.mainJobScreen) {
      if (!allReferencesExist) {
        break;
      }
      const referenceExistsInJob = this.jobReferences.find(
        (x: JobReferenceDetails) =>
          x.referenceTypeId === clientReference.referenceTypeId,
      );

      if (!referenceExistsInJob) {
        allReferencesExist = false;
      }
    }
    if (!allReferencesExist) {
      return false;
    }
    // If all required references exist we continue on and check if the references requirements are met.
    const requirementCheckList: boolean[] = [];
    let numberOfReferenceId4: number = 0;

    this.jobReferences.forEach((jobRef: JobReferenceDetails, index: number) => {
      const foundClientRef = this.clientDetails.references.mainJobScreen.find(
        (ref: ClientReferenceDetails) =>
          ref.referenceTypeId === jobRef.referenceTypeId,
      );
      if (!foundClientRef) {
        return;
      }
      numberOfReferenceId4 += jobRef.referenceTypeId === 4 ? 1 : 0;
      const referenceIsRequired = [2, 3].includes(
        foundClientRef.requiredTypeId,
      );
      requirementCheckList.push(
        jobRef.referenceTypeId === 4 && numberOfReferenceId4 > 1
          ? true
          : referenceIsRequired && foundClientRef.referenceMask !== ''
            ? jobRef.reference.length === foundClientRef.referenceMask.length
            : referenceIsRequired && jobRef.reference.length === 0
              ? false
              : true,
      );
    });
    return requirementCheckList.every((v) => v === true);
  }

  get pudReferenceRequirementsMet() {
    // Return true if cash sale - no references are required
    if (this.jobDetails.client.id === 'CS') {
      return true;
    }
    if (!this.clientDetails._id) {
      return false;
    }
    // We first check that all required references exist within the job.
    const requirementCheckList: boolean[] = [];
    let allReferencesExist: boolean = true;
    for (const pud of this.pudItems) {
      const isDropoff = pud.legTypeFlag === 'D';

      const references = !isDropoff
        ? pud.pickupReference
        : pud.dropoffReference;
      for (const clientReference of this.clientDetails.references.pudScreen) {
        if (!allReferencesExist) {
          break;
        }

        const referenceRequired = !isDropoff
          ? clientReference.requiredTypeId === 4 ||
            clientReference.requiredTypeId === 5 ||
            clientReference.requiredTypeId === 8 ||
            clientReference.requiredTypeId === 7
          : clientReference.requiredTypeId === 4 ||
            clientReference.requiredTypeId === 6 ||
            clientReference.requiredTypeId === 9 ||
            clientReference.requiredTypeId === 7;

        const referenceExistsInJob = references.find(
          (x: JobReferenceDetails) =>
            x.referenceTypeId === clientReference.referenceTypeId,
        );

        if (!referenceExistsInJob && referenceRequired) {
          allReferencesExist = false;
        }
      }

      if (!allReferencesExist) {
        return false;
      }

      // If all required references exist we continue on and check if the references requirements are met.
      // we keep track of normal references. The client can have up to one required normal reference defined (id === 4).
      // Any additional normal references were manually added and have no requirements.
      let numberOfReferenceId4: number = 0;
      references.forEach((x: JobReferenceDetails, index: number) => {
        const clientReference = this.clientDetails.references.pudScreen.find(
          (ref: ClientReferenceDetails) =>
            ref.referenceTypeId === x.referenceTypeId,
        );

        if (!clientReference) {
          return;
        }

        numberOfReferenceId4 += x.referenceTypeId === 4 ? 1 : 0;

        const referenceRequired = !isDropoff
          ? clientReference.requiredTypeId === 4 ||
            clientReference.requiredTypeId === 5 ||
            clientReference.requiredTypeId === 8 ||
            clientReference.requiredTypeId === 7
          : clientReference.requiredTypeId === 4 ||
            clientReference.requiredTypeId === 6 ||
            clientReference.requiredTypeId === 9 ||
            clientReference.requiredTypeId === 7;

        requirementCheckList.push(
          x.referenceTypeId === 4 && numberOfReferenceId4 > 1
            ? true
            : referenceRequired && clientReference.referenceMask !== ''
              ? x.reference.length === clientReference.referenceMask.length
              : referenceRequired && x.reference.length === 0
                ? false
                : true,
        );
      });
    }
    return requirementCheckList.every((v) => v === true);
  }

  // ===========================================================================
  // JobPricingManagement handlers
  // ===========================================================================
  public accountingTotalsComputedUpdated(value: boolean) {
    this.accountingTotalsComputed = value;
  }
  // Refresh the rates in Pricing Management component to their defaults. This
  // will force the pricing screen to refresh (and ignore any existing rates in
  // the job)
  public refreshRatesToDefault() {
    // Find pricing component using ref
    const jobPricingManagement = this.$refs.jobPricingManagement as any;
    // If we successfully find the component using the ref, then call methods to
    // refresh rates
    if (jobPricingManagement) {
      jobPricingManagement.setDefaultsFromJob();
      jobPricingManagement.refreshRatesToDefault();
    }
  }

  public updateFullJobAccountingDetails(newValue: JobAccountingDetails) {
    let additionalDataToSet: AdditionalAccountingData | null = null;
    if (
      !newValue.additionalData?.distanceRate &&
      !!this.jobAccountingDetails.additionalData?.distanceRate
    ) {
      additionalDataToSet = this.jobAccountingDetails.additionalData;
    }
    this.jobAccountingDetails = initialiseJobAccountingDetails(newValue);
    if (additionalDataToSet) {
      this.jobAccountingDetails.additionalData = additionalDataToSet;
    }
  }

  public setClientMinimumDurationMet(value: boolean) {
    this.clientMinimumDurationMet = value;
  }
  public setDriverMinimumDurationMet(value: boolean) {
    this.driverMinimumDurationMet = value;
  }

  get isFleetOwnerRegisteredForGst() {
    return this.jobDetails.accounting.fleetAssetRates[0] &&
      this.jobDetails.accounting.fleetAssetRates[0].isGstRegistered
      ? this.jobDetails.accounting.fleetAssetRates[0].isGstRegistered
      : false;
  }

  get additionalChargeItemList() {
    return useRootStore().additionalChargeItemList;
  }

  /**
   * Attaches accounting totals to JobDetails and dispatch job update operation
   * including the full JobDetails object.
   */
  public async markJobAsReviewed(): Promise<void> {
    const jobDetails = initialiseJobDetails(this.jobDetails);
    const chargeTypeList = useRootStore().additionalChargeTypeList
      ? useRootStore().additionalChargeTypeList
      : [];
    const clientOutsideMetroRate =
      this.jobAccountingDetails.clientRates[0] &&
      this.jobAccountingDetails.clientRates[0].outsideMetroRate
        ? this.jobAccountingDetails.clientRates[0].outsideMetroRate
        : 0;
    const fleetAssetOutsideMetroRate =
      this.jobAccountingDetails.fleetAssetRates[0] &&
      this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate
        ? this.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate
        : 0;
    const computedTotals: {
      totals: JobAccountingTotals;
      jobDurationData: FinishedJobDetails;
    } | null = this.jobAccountingDetails.returnItemizedTotals(
      jobDetails,
      this.fleetAssetOwnerDetails,
      chargeTypeList,
      this.equipmentHireContracts,
      clientOutsideMetroRate,
      fleetAssetOutsideMetroRate,
      this.clientCommonAddressList,
    );
    if (!computedTotals) {
      return;
    }
    this.jobAccountingDetails.totals = computedTotals.totals
      ? computedTotals.totals
      : new JobAccountingTotals();
    const jobDurationData = computedTotals.jobDurationData;
    generateAccountingFinishedJobData(
      jobDetails,
      this.jobAccountingDetails,
      jobDurationData,
      this.driverRegisteredForGst,
    );
    // Assign local copies of data to original
    jobDetails.accounting = this.jobAccountingDetails;
    jobDetails.jobReference = this.jobReferences;

    // Set distances to job before saving
    if (!this.distanceTravelled.planned) {
      this.setPlannedDistanceFromRoute(jobDetails.plannedRoute);
    }
    jobDetails.distanceTravelled = this.distanceTravelled;

    // Updating working job copy with any reference changes in local pudItem
    // list
    this.setUpdatedPudRequirements(jobDetails, this.pudItems);

    // Send request to update
    this.awaitingJobUpdateResponse = true;
    const updateResult = await this.jobStore.updateJobStatus(
      jobDetails.jobId,
      JobEventType.ReviewJob,
      jobDetails,
    );

    // Show notification and return if the request was unsuccessful (null
    // response)
    if (!updateResult) {
      this.showAppNotification(
        `${GENERIC_ERROR_MESSAGE} Job ${this.jobDetails.displayId} could not be marked as reviewed. Please try again later.`,
        HealthLevel.ERROR,
        'Review',
      );
      this.awaitingJobUpdateResponse = false;
      return;
    }
    // Send requests to update notes
    for (const note of this.newJobNotes) {
      this.jobStore.addNoteToJob({ jobId: this.jobDetails.jobId!, note });
    }
    this.awaitingJobUpdateResponse = false;
    this.showAppNotification(
      `Job ${this.jobDetails.displayId} has been marked as reviewed.`,
      HealthLevel.SUCCESS,
      'Review',
    );
  }

  /**
   * Called from the Accounting Totals tab. Sends request to transition to job
   * to READY_FOR_INVOICING work status.
   */
  public async markJobAsFinalised() {
    this.awaitingJobUpdateResponse = true;
    const result = await this.jobStore.updateJobStatus(
      this.jobDetails.jobId,
      JobEventType.ReadyForInvoicing,
    );
    this.awaitingJobUpdateResponse = false;
    if (!result) {
      this.showAppNotification(
        `${GENERIC_ERROR_MESSAGE} Job ${this.jobDetails.displayId} could not be finalised. Please try again later.`,
        HealthLevel.ERROR,
        'Finalisation',
      );
      return;
    }
    this.closeJobDetailsDialog();
  }

  get driverRegisteredForGst(): boolean {
    if (!this.truckDetails) {
      return false;
    }
    if (!this.fleetAssetOwnerDetails) {
      return false;
    }
    return this.fleetAssetOwnerDetails.gstRegistered;
  }

  /**
   * Returns true if all hire contracts in the additionalAssets list of the job
   * have their associated documents in the appliedHireContracts list, AND are
   * valid for the workDate of the job
   */
  get allHireContractsValid(): boolean {
    return allAdditionalAssetsValidForPricing(
      this.jobDetails.additionalAssets,
      this.appliedHireContracts,
      this.jobDetails.workDate,
    );
  }

  public editJobInBookingScreen(jobId: number) {
    if (this.jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED) {
      this.operationsStore.setSelectedBookingScreenJobId(jobId);
      this.closeJobDetailsDialog();
    } else if (this.jobDetails.workStatus === WorkStatus.REVIEWED) {
      this.setReviewedJobToCompleted();
    } else {
      this.operationsStore.setSelectedBookingScreenJobId(jobId);
      this.closeJobDetailsDialog();
    }
  }

  public setReviewedJobToCompleted() {
    if (this.jobDetails.jobId) {
      this.operationsStore.setViewingJobNotesDialog(true);
      this.operationsStore.setJobReleaseForEditing(true);
      this.operationsStore.setJobServiceFailure(false);
      this.operationsStore.setJobCancellation(false);
    }
  }

  get truckDetails() {
    if (
      this.jobDetails !== undefined &&
      this.jobDetails.fleetAssetId !== null &&
      this.jobDetails.fleetAssetId !== ''
    ) {
      const truck = this.fleetAssetStore.getFleetAssetFromFleetAssetId(
        this.jobDetails.fleetAssetId,
      );
      return truck ? truck : undefined;
    } else {
      return undefined;
    }
  }

  get isOutsideHire(): boolean {
    return (
      this.jobDetails.additionalJobData !== undefined &&
      this.jobDetails.additionalJobData.isOutsideHire
    );
  }
  // This job will not be progressed through a mobile app user. Shows and hides
  // controls to the user that allows the job to be manually started and
  // finished. TO DO: Replace 'Outside Hire' labels with 'UNTRACKED'
  get manualProgressionRequired(): boolean {
    return (
      this.jobDetails.additionalJobData !== undefined &&
      this.jobDetails.additionalJobData.manualProgressionRequired
    );
  }
  // If a job is an Outside Hire or Untracked type job, then return a label for
  // use in the HTML to reflect the type of special job it is
  get jobTrackingTypeLabel(): string {
    if (!this.jobDetails.additionalJobData) {
      return '';
    }
    if (this.jobDetails.additionalJobData.isOutsideHire) {
      return 'Outside Hire';
    } else if (this.jobDetails.additionalJobData.manualProgressionRequired) {
      return 'Untracked';
    } else {
      return '';
    }
  }

  /**
   * Returns true of the current job details currently has any additional assets
   * allocated to it. Used in the template.
   */
  get isEquipmentHire(): boolean {
    return this.checkIfJobContainsEquipmentHire(this.jobDetails);
  }

  get minimumDurationRequirementsMet() {
    let clientMinMet: boolean = true;
    let driverMinMet: boolean = true;

    if (!this.accountingTotalsComputed) {
      return false;
    }
    if (this.jobAccountingDetails.clientRates[0].rate.rateTypeId === 1) {
      clientMinMet = this.clientMinimumDurationMet;
    }

    if (this.jobAccountingDetails.fleetAssetRates[0].rate.rateTypeId === 1) {
      driverMinMet = this.driverMinimumDurationMet;
    }
    return clientMinMet && driverMinMet;
  }

  get fleetAssetOwnerDetails(): FleetAssetOwnerSummary | null {
    if (this.truckDetails !== null && this.truckDetails !== undefined) {
      const owner = useFleetAssetOwnerStore().getOwnerFromOwnerId(
        this.truckDetails.fleetAssetOwnerId,
      );
      return owner ? owner : null;
    } else {
      return null;
    }
  }

  get rebookJobDisabled(): boolean {
    // If client is CS, allow rebooking
    if (this.jobDetails.client.id === 'CS') {
      return false;
    }

    // Disable if client status is in 3, 7, 13
    const clientStatusDisabled = [3, 7, 13].some((status) =>
      this.clientDetails.statusList.includes(status),
    );

    // Disable if job status is in 57, 45
    const jobStatusDisabled = [57, 45].some((status) =>
      this.jobDetails.statusList.includes(status),
    );

    return clientStatusDisabled || jobStatusDisabled;
  }

  // Called when job is manually being set to the Started for Outside Hire type job
  public updateJobStatusStarted() {
    const jobId = this.jobDetails.jobId;
    if (!jobId) {
      return;
    }
    this.jobStore.updateJobStatus(
      this.jobDetails.jobId,
      JobEventType.StartedJob,
    );
  }
  // Called when job is manually being set to COMPLETED for Outside Hire type
  // job
  public updateJobStatusComplete() {
    const jobId = this.jobDetails.jobId;
    if (!jobId) {
      return;
    }
    const foundStartedEvent =
      this.jobDetails.returnSpecifiedEvent('StartedJob');

    for (let i = 0; i < this.jobDetails.pudItems.length; i++) {
      const pud = this.jobDetails.pudItems[i];
      if (!['P', 'D'].includes(pud.legTypeFlag)) {
        continue;
      }

      let arrivedEventTime;

      if (i === 0 && foundStartedEvent) {
        arrivedEventTime = foundStartedEvent.correctEventTime;
      } else {
        arrivedEventTime = moment().valueOf();
      }

      const arrivedEvent: JobStatusUpdate = Object.assign(
        new JobStatusUpdate(),
        {
          id: uuidv4().replace(/-/g, ''),
          jobId,
          pudId: pud.pudId,
          changeTime: arrivedEventTime,
          updatedStatus: 'ARRIVED',
          editedBy: sessionManager.getActiveUser(),
        },
      );

      const finishedEvent: JobStatusUpdate = Object.assign(
        new JobStatusUpdate(),
        {
          id: uuidv4().replace(/-/g, ''),
          jobId,
          pudId: pud.pudId,
          changeTime: moment().valueOf(),
          updatedStatus: 'FINISHED',
          editedBy: sessionManager.getActiveUser(),
        },
      );
      this.jobDetails.eventList.push(arrivedEvent);
      this.jobDetails.eventList.push(finishedEvent);
      pud.status = 'FINISHED';
    }
    this.jobStore.updateJobStatus(
      this.jobDetails.jobId,
      JobEventType.CompletedJob,
      this.jobDetails,
    );
  }
  // Assign any changes from the local list to the JobDetails list before saving
  public setUpdatedPudRequirements(
    jobDetails: JobDetails,
    pudItems: PUDItem[],
  ) {
    jobDetails.pudItems.forEach((pud) => {
      // Find associated PUD Item
      const foundPud = pudItems.find((p) => p.pudId === pud.pudId);
      if (foundPud) {
        // set weight
        pud.weight = foundPud.weight;
        // Set pickup references for Pickup type
        if (pud.legTypeFlag === 'P') {
          pud.pickupReference = foundPud.pickupReference.filter(
            (r) => !!r.reference,
          );
          pud.manifest = foundPud.manifest;
        }
        // Set dropoff references for Dropoff type
        if (pud.legTypeFlag === 'D') {
          pud.dropoffReference = foundPud.dropoffReference.filter(
            (r) => !!r.reference,
          );
        }
      }
    });
  }

  // Request GPS Data
  public async requestJobGpsData() {
    const request = this.jobDetails.returnJobGpsRequest();
    if (request !== undefined) {
      this.isRequestingJobGpsData = true;
      const results = await useGpsStore().getGPSDataByJob(request);
      if (results?.length) {
        this.jobGpsData = validateGpsData(results);
        if (this.jobGpsData.length) {
          // If we have the coordinates we can calculate the distance
          // manually rather than requesting them
          if (!this.distanceTravelled.gpsEstimate) {
            const distanceInMetres = distanceFromPoints(this.jobGpsData);
            this.distanceTravelled.gpsEstimate = RoundCurrencyValue(
              distanceInMetres / 1000,
              3,
            );
          }
        }
      }
      this.isRequestingJobGpsData = false;
    }
  }

  /**
   * Called from dialog menu. Sends request to mobile app to retrieve device
   * information for the driver's mobile device.
   */
  public async requestDriverDeviceInfo() {
    if (this.driverDeviceSnapshot === null) {
      this.driverDeviceSnapshot = null;
      this.dispatchDeviceQueryRequest(DeviceQueryType.DEVICE_SUMMARY);
    }
    this.isViewingDriverDeviceInfoDialog = true;
  }

  /**
   * Send an action to the driver's mobile device This is a oneway message -
   * there is currently no way to tell if the response was successfully
   * received/processed by the mobile app.
   * @param queryType - The type of device query to be dispatched.
   */
  public pushDriverDeviceEvent(queryType: DeviceQueryType) {
    this.dispatchDeviceQueryRequest(queryType);
    this.showAppNotification(
      'Sent Request to Driver Mobile App',
      HealthLevel.INFO,
      'Device Query',
    );
  }

  // Trigger app notification. Defaults to ERROR type message, but type can be
  // provided to produce other types. Includes componentTitle as a title for the
  // notification.
  public showAppNotification(
    text: string,
    type?: HealthLevel,
    subtitle?: string,
  ): void {
    showNotification(text, {
      type,
      title:
        `Job #${this.jobDetails.displayId}` +
        (subtitle ? ` - ${subtitle}` : ''),
    });
  }

  /**
   * Dispatches a device query request based on the specified query type. If the
   * query type is DEVICE_SUMMARY, it waits for the response and updates the UI
   * accordingly. If the query type is not DEVICE_SUMMARY, it sends the event
   * without waiting for a response.
   *
   * @param queryType - The type of device query to be dispatched.
   */
  public async dispatchDeviceQueryRequest(queryType: DeviceQueryType) {
    const request: DriverDeviceQuery = {
      driverId: this.jobDetails.driverId,
      queryType,
    };
    // If we're fetching a DEVICE_SUMMARY, we need to wait for the response so
    // we can display
    const awaitResponse = queryType === DeviceQueryType.DEVICE_SUMMARY;
    if (awaitResponse) {
      const result = await useRootStore().requestDriverDeviceInfo(request);
      if (result) {
        this.driverDeviceSnapshot = result;
        this.updateGpsMarkerDetailsInState(result);
      } else {
        this.showAppNotification(
          'Something went wrong. Device Query could not be retrieved.',
        );
        this.cancelDriverDeviceInfoView();
      }
    } else {
      // If not a DEVICE SUMMARY, we can just send the event and not wait for a
      // response
      useRootStore().pushDriverDeviceEvent(request);
    }
  }

  /**
   * Closes the driver device info dialog and resets the driverDeviceSnapshot
   */
  public cancelDriverDeviceInfoView() {
    this.isViewingDriverDeviceInfoDialog = false;
    this.driverDeviceSnapshot = null;
  }

  public updateGpsMarkerDetailsInState(deviceSummary: DriverDeviceSnapshot) {
    // get last known GPS from state
    const gpsMarkerDetails: GpsMarkerDetails | undefined =
      useGpsStore().allGpsPositions.get(this.jobDetails.fleetAssetId);
    if (!gpsMarkerDetails) {
      return;
    }
    if (
      !deviceSummary.lastLatitude ||
      !deviceSummary.lastLatitude ||
      deviceSummary.timestamp < gpsMarkerDetails.timestamp
    ) {
      return;
    }
    const latitude = parseFloat(deviceSummary.lastLatitude);
    const longitude = parseFloat(deviceSummary.lastLongitude);
    gpsMarkerDetails.latitude = latitude;
    gpsMarkerDetails.longitude = longitude;
  }

  // Helper function to console.log some details when the dialog banner is clicked on
  public logJobIdentifiers() {
    console.info(
      `J-${this.jobDetails.jobId} D-${this.jobDetails.driverId} F-${this.jobDetails.fleetAssetId}`,
    );
  }
  // Force the 'Mark As Reviewed' confirmation dialog to re-run mounted logic
  // when the conditions change, such that the appropriate checkbox labels are
  // displayed when data changes
  get confirmationDialogKey() {
    return `${this.isOutsideHire}-${this.clientHasReferenceRequirements}-${this.requiredBreakDurationsMet}-${this.requiredBreakForJobLength}`;
  }

  // Returns true if the amount of current breaks taken
  get requiredBreakDurationsMet() {
    if (
      !this.jobAccountingDetails ||
      !this.jobAccountingDetails.clientRates[0] ||
      !this.jobAccountingDetails.fleetAssetRates[0]
    ) {
      return false;
    }
    if (this.requiredBreakForJobLength === 0) {
      return true;
    }
    const breakDur =
      this.jobAccountingDetails.fleetAssetRates[0].breakDuration?.breakSummaryList?.reduce(
        (total, curr) => total + curr.durationInMilliseconds,
        0,
      ) ?? 0;

    return breakDur >= this.requiredBreakForJobLength;
  }
  // Calculate the required break duration in milliseconds based on the Fleet
  // Asset times
  get requiredBreakForJobLength(): number {
    if (!this.jobAccountingDetails) {
      return 0;
    }
    return this.jobAccountingDetails.requiredBreakForJobLength
      ? this.jobAccountingDetails.requiredBreakForJobLength
      : 0;
  }

  // find if the client has reference requirements. If the client does we need
  // to have the user confirm they have checked the references when they price the job.
  get clientHasReferenceRequirements(): boolean {
    if (!this.clientDetails) {
      return false;
    }
    const references = this.clientDetails.references.mainJobScreen.concat(
      this.clientDetails.references.pudScreen,
    );

    if (references.length === 0) {
      return false;
    }

    let clientHasReferenceRequirement: boolean = false;

    for (const reference of references) {
      if (clientHasReferenceRequirement) {
        break;
      }

      clientHasReferenceRequirement = reference.requiredTypeId !== 1;
    }

    return clientHasReferenceRequirement;
  }

  get clientHasWeightRequirements() {
    return (
      this.clientDetails.weightRequirementId &&
      this.clientDetails.weightRequirementId !== 7
    );
  }

  get pricingConfirmationCheckList(): string[] {
    const checkList: string[] = [];

    if (this.isOutsideHire) {
      checkList.push(
        'I have received all required photos and paperwork from the Outside Hire, and confirmed pricing',
      );
    }
    if (this.clientHasReferenceRequirements) {
      checkList.push(
        'I have checked that the references are valid references prior to pricing this job',
      );
    }

    if (this.clientHasWeightRequirements) {
      checkList.push(
        'I have checked that all weights are correct prior to pricing this job',
      );
    }
    if (!this.requiredBreakDurationsMet) {
      const minBreakReq = returnDurationFromMilliseconds(
        this.requiredBreakForJobLength,
      );
      checkList.push(
        `The duration of this job indicates that it should have a break (at least ${minBreakReq}). I confirm I have reviewed the relevant break requirements for this driver.`,
      );
    }
    return checkList;
  }

  // Force remount of pricing component (PRI) after adjust service details
  // confirmation emit event. We do this to enforce that the changes are
  // accounted for via the remount
  public refreshAccountingInParent(accounting: JobAccountingDetails | null) {
    if (accounting) {
      this.updateFullJobAccountingDetails(accounting);
    }
    const currentView = this.selectedViewType;
    if (currentView === 'PRI') {
      this.setSelectedView('');
      this.$nextTick(() => {
        this.setSelectedView(currentView);
      });
    }
  }

  // Set integration requirements for this client
  public async getClientEDIRequirements() {
    // Check if the job was sourced from our API Integration service.
    if (this.jobDetails.exportType === JobSourceType.API) {
      // Find if this job was booked in via the api service or this job is linked to unassigned puds
      this.showEDIConfirmation = true;
      return;
    }
    // If the jobs source type is not known we will be required to check if the
    // client is associated with any users in our EDI api service
    const apiRequest: APIListUsernamesForClientRequest = {
      company: this.jobDetails.company,
      division: this.jobDetails.division,
      clientId: this.jobDetails.client.id,
    };
    this.setClientEDIRequirements(
      await this.dataImportStore.getApiUsernamesForClient(apiRequest),
    );
  }

  // Handler for request called in getClientEDIRequirements. If this clients
  // username list is populated it means there will be requirements for the user
  // to check over the job before marking as reviewed.
  public setClientEDIRequirements(
    response: APIListUsernamesForClientResponse | null,
  ) {
    // if response was null or the response was for a client that is not on this job we will set showEDIConfirmation to false and return
    if (
      !response?.clientId ||
      response.clientId !== this.jobDetails.client.id
    ) {
      this.showEDIConfirmation = false;
      return;
    }
    // If there is more than one username in the response we will be required to show the integration_requirement component.
    this.showEDIConfirmation =
      response.usernameList.length > 0 &&
      this.jobDetails.jobSourceType !== JobSourceType.IMPORT;
  }

  /**
   * Checks if the job is allocated any additionalAssets (equipment hire). Used
   * to determine if certain equipment hire-specific APIs and components should
   * be called/displayed.
   * @param jobDetails - The job details object.
   * @returns `true` if equipment hire exists; otherwise, `false`.
   */
  private checkIfJobContainsEquipmentHire(jobDetails: JobDetails): boolean {
    const equipmentHireExists = (
      jobDetails.additionalAssets ? jobDetails.additionalAssets : []
    ).filter((x: AdditionalAsset) => x.contractId !== '');
    return equipmentHireExists.length > 0;
  }

  /**
   * Requests equipment hire contracts for the specified job details if
   * equipment hire is required. The response is added to the local list of hire
   * contracts for use in the accounting calculations.
   * @param jobDetails - The details of the job, including additional assets and
   * the work date.
   */
  public async requestEquipmentHireContractsIfRequired(
    jobDetails: JobDetails,
  ): Promise<void> {
    if (!this.checkIfJobContainsEquipmentHire(jobDetails)) {
      this.appliedHireContracts = [];
      return;
    }
    // Remove any elements from appliedHireContracts that are not in the
    // additionalAssets list of the job
    this.appliedHireContracts = this.appliedHireContracts.filter((c) =>
      jobDetails.additionalAssets.some(
        (x: AdditionalAsset) => x.contractId === c._id,
      ),
    );

    // Get the contractIds from the additionalAssets list
    // Filter out any contractIds that are empty
    // Filter out any contractIds that are already in the appliedHireContracts
    const contractIds = jobDetails.additionalAssets
      .filter((x: AdditionalAsset) => x.contractId !== '')
      .filter(
        (x: AdditionalAsset) =>
          !this.appliedHireContracts.some(
            (c: HireContract) => c._id === x.contractId,
          ),
      )
      .map((c) => c.contractId);

    // Request hire contracts for each assetId, for the workDate of the job
    const hireContractPromises = contractIds.map((contractId) =>
      this.fleetAssetStore.requestHireContractById(contractId),
    );

    // Wait for all promises to resolve
    const result: (HireContract | null)[] =
      await Promise.all(hireContractPromises);
    result.forEach((res) => {
      if (res) {
        this.appliedHireContracts.push(res);
      }
    });
  }

  // Client Details and set local working variables
  public async prepareRequiredData() {
    // Check if we need to request any tab-specific information for the current
    // tab we're loading onto
    await this.requestMissingDataForViewType(this.selectedViewType);

    // Request hire contracts
    await this.requestEquipmentHireContractsIfRequired(this.jobDetails);

    // Request client details. If the job is a cash sale we will not need to
    // request the client details
    if (this.jobDetails.client.id !== 'CS') {
      await this.requestClientSpecificData();
      if (this.jobDetails.accounting) {
        const additionalData = await returnAdditionalAccountingData({
          jobDetails: this.jobDetails,
          applicableRateTypes: [JobRateType.DISTANCE],
          forceRequest: false,
        });
        this.jobDetails.accounting.additionalData = additionalData;
        this.jobAccountingDetails.additionalData = deepCopy(additionalData);
      }
    }
    this.setJobRequirementsData(this.jobDetails);
    this.isLoading = false;
  }

  /**
   * Request the client details for the current job, as well as all key data
   * related to the client (such as common addresses)
   */
  public async requestClientSpecificData() {
    const clientDetails =
      await this.clientDetailsStore.requestClientDetailsByClientId(
        this.jobDetails.client.id,
      );
    if (clientDetails) {
      this.clientDetails = clientDetails;
      this.clientCommonAddressList =
        (await this.clientDetailsStore.requestCommonAddressesByClientId(
          clientDetails.clientId,
        )) ?? [];
    }
    this.getClientEDIRequirements();
  }

  public isAuthorised(): boolean {
    return hasAdminOrTeamLeaderOrBranchManagerRole();
  }

  get isAuthorisedAdminAndWorkStatus(): boolean {
    const workStatus = this.jobDetails.workStatus;
    return (
      hasAdminRole() &&
      workStatus >= WorkStatus.ALLOCATED &&
      workStatus <= WorkStatus.DRIVER_COMPLETED
    );
  }

  /**
   * Mitt callback for jobStatusUpdate when a job is set to completed from the
   * note component. Set the active tab to the notes tab.
   * @param payload JobEventSummary payload
   */
  private handleCompletedJobUpdate(payload: JobEventSummary | null) {
    // TODO: Add additional handling here to check if we need to re-set the references list
    if (payload?.jobId === this.jobDetails.jobId) {
      // Jump to pricing tab if the request was the current user sending the job back
      if (
        payload?.event === 'CompletedJob' &&
        this.operationsStore.releaseForEditingRequestMade
      ) {
        this.operationsStore.setReleaseForEditingRequestMade(false);
        this.setSelectedView('PRI');
      }
      // If the event was 'UpdateJobDetails' we need to update the pudItems and
      // jobReferences so that the local working variables are up to date
      if (payload?.event === 'UpdateJobDetails' && !!payload?.jobDetails) {
        // If the job was updated by another user we need to update the local
        // working variables
        if (!this.awaitingJobReferencesSaveRequest) {
          if (this.editingReferenceFormController) {
            this.showAppNotification(
              `Job #${this.jobDetails.displayId} has been updated by another user. Your changes have been discarded.`,
              HealthLevel.WARNING,
              'Requirements',
            );
            this.isEditingForm = false;
          }
          this.setJobRequirementsData(payload.jobDetails);
        }
        // Update hire contracts
        this.requestEquipmentHireContractsIfRequired(payload.jobDetails);
      }
    }
  }

  public async mounted() {
    document.addEventListener('keydown', this.handleEscapePress);

    if (!this.jobDetails) {
      return;
    }
    this.prepareRequiredData();
    Mitt.on('jobStatusUpdate', this.handleCompletedJobUpdate);
  }

  public beforeDestroy() {
    document.removeEventListener('keydown', this.handleEscapePress);
    Mitt.off('jobStatusUpdate', this.handleCompletedJobUpdate);
  }

  public handleEscapePress(event: any) {
    if (event.key === 'Escape') {
      this.closeJobDetailsDialog(true);
    }
  }
}

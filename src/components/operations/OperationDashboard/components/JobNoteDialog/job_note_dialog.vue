<template>
  <div class="job-note-dialog" v-if="jobDetails">
    <ContentDialog
      :showDialog.sync="showDialog"
      :title="`Adding note to Job ${jobDetails.displayId}`"
      width="50%"
      contentPadding="pa-0"
      @cancel="showDialog = false"
      :showActions="false"
      :contentClass="
        isJobCancellation && !isEditingExistingNote
          ? 'v-dialog-custom-warning'
          : 'v-dialog-custom'
      "
    >
      <v-layout>
        <v-flex md12 class="body-scrollable--75 body-min-height--65">
          <v-layout class="app-theme__center-content--body">
            <v-alert
              type="error"
              :value="isJobCancellation && !isEditingExistingNote"
              >Cancellation of this job is irreversible.
            </v-alert>
            <v-alert
              type="info"
              :value="
                isDispatchNote &&
                jobHasExistingDispatchNote &&
                !isEditingExistingNote
              "
            >
              This will replace the current Dispatch Note.
            </v-alert>
          </v-layout>
          <v-layout class="app-theme__center-content--body pa-3" wrap>
            <v-flex md4 class="side-column">
              <v-layout>
                <v-flex md4>
                  <v-layout class="side-column__label" justify-end>
                    Job Date:
                  </v-layout>
                  <v-layout class="side-column__label" justify-end>
                    Job Number:
                  </v-layout>
                  <v-layout class="side-column__label" justify-end>
                    Customer:
                  </v-layout>
                  <v-layout
                    class="side-column__label"
                    v-if="
                      isJobServiceFailure ||
                      isJobCancellation ||
                      isJobReleaseForEditing ||
                      isRestoreCancelledJob
                    "
                    justify-end
                  >
                    Type:
                  </v-layout>
                </v-flex>
                <v-flex md8>
                  <v-layout class="side-column__value">
                    {{ jobDetails.jobDate }}
                  </v-layout>
                  <v-layout class="side-column__value">
                    {{ jobDetails.displayId }}
                  </v-layout>
                  <v-layout class="side-column__value">
                    {{ jobDetails.client.clientName }}
                  </v-layout>
                  <v-layout class="side-column__value" v-if="isJobCancellation">
                    Cancel Job
                  </v-layout>
                  <v-layout
                    class="side-column__value"
                    v-if="isJobServiceFailure"
                  >
                    <span v-if="jobHasActiveServiceFailure" class="pr-1"
                      >Remove </span
                    >Service Failure
                  </v-layout>
                  <v-layout
                    class="side-column__value"
                    v-if="isJobReleaseForEditing"
                  >
                    Release For Editing
                  </v-layout>
                  <v-layout
                    class="side-column__value"
                    v-if="isRestoreCancelledJob"
                  >
                    Restore Cancelled Job
                  </v-layout>

                  <v-layout
                    class="side-column__value"
                    v-if="isJobCancellation && !jobHasActiveServiceFailure"
                  >
                    <v-checkbox
                      label="Add Service Failure"
                      v-model="isAlsoServiceFailure"
                    />
                  </v-layout>
                  <v-layout
                    class="side-column__value"
                    v-if="isJobCancellation && jobHasActiveServiceFailure"
                  >
                    Service Failure Already Active
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-flex>
            <v-flex md8>
              <v-layout row wrap>
                <v-flex md12>
                  <NotesEditor
                    :communications="jobDetails.notes"
                    :isEdited="true"
                    :type="3"
                    :isAddingNote="true"
                    :jobDetails="jobDetails"
                    @setIsAddingNote="showDialog = $event"
                    :isJobServiceFailure="isJobServiceFailure"
                    :isJobReleaseForEditing="isJobReleaseForEditing"
                    :isJobCancellation="isJobCancellation"
                    :isDispatchNote="isDispatchNote"
                    :isStartOfDayCheckNote="isStartOfDayCheckNote"
                    :isRestoreCancelledJob="isRestoreCancelledJob"
                    :isEditingExistingNote="isEditingExistingNote"
                    :currentlyEditingNoteDetails="editingExistingCommunication"
                  >
                  </NotesEditor>
                </v-flex>
              </v-layout>
            </v-flex>
          </v-layout>
        </v-flex>
      </v-layout>
    </ContentDialog>
  </div>
</template>
<script setup lang="ts">
import {
  ComputedRef,
  Ref,
  WritableComputedRef,
  computed,
  ref,
  watch,
} from 'vue';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import NotesEditor from '@/components/common/notes_editor/notes_editor.vue';
import AddNoteToJobRequest from '@/interface-models/Generic/Communication/AddNoteToJobRequest';
import ContentDialog from '@/components/common/ui-elements/content_dialog.vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
}>();

const props = withDefaults(
  defineProps<{
    jobDetails: JobDetails;
    showJobNoteDialog?: boolean;
  }>(),
  {
    showJobNoteDialog: false,
  },
);

const operationsStore = useOperationsStore();
const isAlsoServiceFailure: Ref<boolean> = ref(false);

watch(isAlsoServiceFailure, (value: boolean) => {
  operationsStore.setJobServiceFailure(value);
});

/**
 * Getter and setter to control the visibility of the dialog. Returns value from
 * prop, and resets the values in the store when the dialog is closed.
 */
const showDialog: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.showJobNoteDialog ?? false;
  },
  set(value: boolean): void {
    operationsStore.setViewingJobNotesDialog(value);
    operationsStore.setEditingCommunicationInDialog(null);
    operationsStore.setJobServiceFailure(false);
    operationsStore.setJobCancellation(false);
    operationsStore.setJobReleaseForEditing(false);
    operationsStore.setRestoreCancelledJob(false);
    operationsStore.setDispatchNote(false);
    operationsStore.setStartOfDayCheckNote(false);
  },
});

const isJobServiceFailure: ComputedRef<boolean> = computed(() => {
  return operationsStore.isJobServiceFailure;
});

const isJobReleaseForEditing: ComputedRef<boolean> = computed(() => {
  return operationsStore.isJobReleaseForEditing;
});

const isJobCancellation: ComputedRef<boolean> = computed(() => {
  return operationsStore.isJobCancellation;
});

const isRestoreCancelledJob: ComputedRef<boolean> = computed(() => {
  return operationsStore.isRestoreCancelledJob;
});

const isDispatchNote: ComputedRef<boolean> = computed(() => {
  return operationsStore.isDispatchNote;
});

const isStartOfDayCheckNote: ComputedRef<boolean> = computed(() => {
  return operationsStore.isStartOfDayCheckNote;
});

const jobHasExistingDispatchNote: ComputedRef<boolean> = computed(() => {
  return props.jobDetails?.dispatchNote !== undefined;
});

const jobHasActiveServiceFailure: ComputedRef<boolean> = computed(() => {
  return props.jobDetails?.serviceFailure ?? false;
});

const isEditingExistingNote: ComputedRef<boolean> = computed(() => {
  return editingExistingCommunication.value !== undefined;
});

const editingExistingCommunication: ComputedRef<
  AddNoteToJobRequest | undefined
> = computed(() => {
  const editing = operationsStore.editingCommunicationInDialog;
  if (editing !== null && editing.note.id) {
    return editing;
  }
  return undefined;
});
</script>

<style scoped lang="scss">
.job-note-dialog {
  padding: 0;
  position: relative;
}
.alert-container {
  margin: 2px;
}
.side-column {
  height: 100%;

  .side-column__label {
    font-size: $font-size-12;
    text-transform: uppercase;
    font-weight: 500;
    color: rgb(186, 188, 209) !important;
    padding-right: 12px;
    padding-top: 3px;
    letter-spacing: 0.02em;
  }

  .side-column__value {
    font-size: $font-size-12;
    font-weight: 600;
    padding-top: 3px;
  }
}
</style>

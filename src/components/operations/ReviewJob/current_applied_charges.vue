<template>
  <section class="current-applied-charges pt-0">
    <v-layout row wrap>
      <v-flex md12 pt-1 class="expansion-panel-container" px-1>
        <v-layout class="expansion-item-container">
          <!-- Shared: Break Duration Row -->
          <v-flex md12>
            <table class="applied-charges-table">
              <tr>
                <td class="lineitem__label">Break Duration</td>
                <td></td>
                <td class="lineitem__value">
                  {{ currentBreakDuration ? currentBreakDuration : '-' }}
                  <span v-if="breakDurationBreakdown">
                    <v-tooltip left>
                      <template v-slot:activator="{ on }">
                        <v-icon size="13" class="pl-1" color="info" v-on="on">
                          fas fa-exclamation-circle
                        </v-icon>
                      </template>
                      <span>{{ breakDurationBreakdown }}</span>
                    </v-tooltip>
                  </span>
                  <span
                    v-if="
                      entityType === RateEntityType.FLEET_ASSET &&
                      !requiredBreakDurationsMet
                    "
                  >
                    <v-tooltip left>
                      <template v-slot:activator="{ on }">
                        <v-icon size="13" class="pl-1" color="amber" v-on="on">
                          fas fa-exclamation-triangle
                        </v-icon>
                      </template>
                      <span>
                        Minimum break of
                        {{
                          requiredBreakForJobLength
                            ? returnDurationFromMilliseconds(
                                requiredBreakForJobLength,
                              )
                            : '-'
                        }}
                        is required.
                      </span>
                    </v-tooltip>
                  </span>
                  <v-icon
                    @click="openBreakDurationDialog"
                    class="edit-break-icon"
                    icon
                    small
                    text
                  >
                    {{ !readOnly ? `fal fa-edit` : `fal fa-search` }}
                  </v-icon>
                </td>
              </tr>
              <!-- Shared: Fuel Levy Row -->
              <tr v-if="fuelSurcharge !== undefined">
                <td class="lineitem__label">Fuel Levy</td>
                <td></td>
                <td class="lineitem__value">
                  {{
                    fuelSurcharge.fuelSurchargeRate
                      ? fuelSurcharge.fuelSurchargeRate
                      : 0
                  }}%
                </td>
              </tr>
              <!-- Shared: Outside Metro Row -->
              <tr v-if="showOutsideMetro">
                <td class="lineitem__label">
                  <v-layout align-center>
                    Outside Metro
                    <input
                      v-if="!readOnly"
                      class="ml-2"
                      style="cursor: pointer"
                      type="checkbox"
                      id="outsideHireCheck"
                      name="outsideHireCheck"
                      v-model="outsideMetroModel"
                    />
                  </v-layout>
                </td>
                <td></td>
                <td
                  class="lineitem__value"
                  v-if="!editClientOutsideMetro && !editDriverOutsideMetro"
                >
                  <span v-if="outsideMetroRate !== undefined">
                    {{ outsideMetroRate }}%
                  </span>
                  <v-icon
                    v-if="!readOnly"
                    size="12"
                    class="edit-break-icon"
                    @click="editOutsideMetro(props.entityType)"
                  >
                    fal fa-edit
                  </v-icon>
                </td>
                <td v-else>
                  <v-text-field
                    type="number"
                    :label="outsideMetroEditLabel"
                    v-model.number="outsideMetroEditRate"
                    box
                    hide-details
                    class="mb-2"
                  />
                  <v-layout justify-space-between>
                    <v-btn
                      class="ma-0"
                      outline
                      small
                      depressed
                      color="error"
                      @click="cancelOutsideMetroEdit"
                    >
                      Cancel
                    </v-btn>
                    <v-btn
                      outline
                      color="info"
                      @click="saveEditedOutsideMetroRate"
                      class="ma-0"
                      small
                      depressed
                    >
                      Apply
                    </v-btn>
                  </v-layout>
                </td>
              </tr>
              <!-- Unified: Additional Charges Rows -->
              <tr
                v-for="(item, index) in filteredAdditionalCharges"
                :key="item._id ?? index"
              >
                <template v-if="!!chargeConfig(item).charge">
                  <td class="lineitem__label">{{ item.longName }}</td>
                  <td class="reduce-padding">
                    <div
                      class="quantity-container"
                      v-if="item._id !== tollAdminAndHandlingId"
                    >
                      <template v-if="readOnly">
                        <v-flex md12>
                          <v-layout align-center>
                            <span class="quantity-text">Qty:</span>
                            <span class="quantity-amount">{{
                              item.quantity
                            }}</span>
                          </v-layout>
                        </v-flex>
                      </template>
                      <template v-else>
                        <div class="scaled-input-wrapper">
                          <v-text-field
                            class="v-solo-custom v-solo-custom--compact"
                            type="number"
                            solo
                            flat
                            prefix="Qty:"
                            color="light-blue"
                            label="Quantity"
                            @focus="$event.target.select()"
                            v-model="item.quantity"
                            :rules="[
                              validationRules.required,
                              validationRules.number,
                              validationRules.nonNegative,
                            ]"
                            hide-details
                            validate-on-blur
                            @change="
                              updateAdditionalChargeItem({
                                type: AdditionalChargeUpdateType.SET_QUANTITY,
                                id: item._id,
                                quantity: $event,
                              })
                            "
                            @wheel.prevent
                            @keydown.up.prevent="
                              updateAdditionalChargeItem({
                                type: AdditionalChargeUpdateType.INCREMENT,
                                id: item._id,
                              })
                            "
                            @keydown.down.prevent="
                              updateAdditionalChargeItem({
                                type: AdditionalChargeUpdateType.DECREMENT,
                                id: item._id,
                              })
                            "
                          ></v-text-field>
                        </div>
                      </template>
                    </div>
                  </td>
                  <td class="lineitem__value">
                    <span
                      class="pr-2"
                      v-if="item._id !== tollAdminAndHandlingId"
                    >
                      @
                    </span>
                    {{ appliedAdditionalCharge(item, entityType) }}
                    <v-icon
                      size="14"
                      class="pl-1"
                      @click="
                        updateAdditionalChargeItem({
                          type: AdditionalChargeUpdateType.REMOVE,
                          id: item._id,
                        })
                      "
                      v-if="!readOnly && item._id !== tollAdminAndHandlingId"
                    >
                      far fa-times
                    </v-icon>
                  </td>
                </template>
              </tr>
            </table>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 mt-1>
        <v-layout v-if="!readOnly" justify-end>
          <v-btn
            block
            outline
            @click="addNewCharge(true)"
            color="info"
            elevation="2"
            >Add New Charge <v-icon right size="16">fal fa-plus </v-icon>
          </v-btn>
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import { returnDurationFromMilliseconds } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { validateAdditionalChargeUpdate } from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeRateConfig } from '@/interface-models/AdditionalCharges/AdditionalChargeRateConfig';
import {
  AdditionalChargeUpdateType,
  UpdateAdditionalChargeItem,
} from '@/interface-models/AdditionalCharges/AdditionalChargeUpdateOperation';
import { AdditionalChargeList } from '@/interface-models/Generic/Accounting/AdditionalChargeList';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import {
  computed,
  ComputedRef,
  defineEmits,
  Ref,
  ref,
  WritableComputedRef,
} from 'vue';

const emit = defineEmits<{
  (event: 'update:isDialogOpen', payload: boolean): void;
  (event: 'updateForceOutsideMetroChargeClient', value: boolean): void;
  (event: 'updateForceOutsideMetroChargeFleetAsset', value: boolean): void;
  (event: 'addNewAdditionalCharge', value: boolean): void;
  // (
  //   event: 'updateList',
  //   removalInformation: { charge: AdditionalChargeItem; index: number },
  // ): void;
  // (event: 'tollAdminAndServicesFeeHandler'): void;
  (event: 'setViewingBreakDurationDialog', value: boolean): void;
  (event: 'updateAdditionalCharge', payload: UpdateAdditionalChargeItem): void;
}>();

const props = withDefaults(
  defineProps<{
    entityType: RateEntityType;
    invoiceCharges: AdditionalChargeList;
    clientFuelSurcharge: ClientFuelSurchargeRate;
    fleetAssetFuelSurcharge: ClientFuelSurchargeRate;
    jobAccountingDetails: JobAccountingDetails;
    readOnly?: boolean;
    isOutsideHire?: boolean;
    newJobNotes?: Communication[];
    forceOutsideMetroChargeClient?: boolean;
    forceOutsideMetroChargeFleetAsset?: boolean;
  }>(),
  {
    readOnly: false,
    isOutsideHire: false,
    forceOutsideMetroChargeClient: false,
    forceOutsideMetroChargeFleetAsset: false,
    newJobNotes: () => [],
  },
);

const editDriverOutsideMetro: Ref<boolean> = ref(false);
const editClientOutsideMetro: Ref<boolean> = ref(false);
const outsideMetroEditLabel: Ref<string> = ref('');
const outsideMetroEditRate: Ref<number | null> = ref(null);

const additionalChargeList: ComputedRef<AdditionalChargeItem[]> = computed(
  () => {
    return props.invoiceCharges.chargeList;
  },
);

const isForceOutsideMetroChargeClient: WritableComputedRef<boolean> = computed({
  get(): boolean {
    return props.forceOutsideMetroChargeClient;
  },
  set(forceCharge: boolean): void {
    emit('updateForceOutsideMetroChargeClient', forceCharge);
  },
});

const isForceOutsideMetroChargeFleetAsset: WritableComputedRef<boolean> =
  computed({
    get(): boolean {
      return props.forceOutsideMetroChargeFleetAsset;
    },
    set(forceCharge: boolean) {
      emit('updateForceOutsideMetroChargeFleetAsset', forceCharge);
    },
  });

function editOutsideMetro(type: RateEntityType) {
  if (type === RateEntityType.CLIENT) {
    editClientOutsideMetro.value = true;
    outsideMetroEditRate.value = deepCopy(
      props.jobAccountingDetails.clientRates[0].outsideMetroRate,
    );
    outsideMetroEditLabel.value = 'Client Outside Metro Rate (%)';
  } else if (type === RateEntityType.FLEET_ASSET) {
    editDriverOutsideMetro.value = true;
    outsideMetroEditRate.value = deepCopy(
      props.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate,
    );
    outsideMetroEditLabel.value = 'Driver Outside Metro Rate (%)';
  }
}

function saveEditedOutsideMetroRate() {
  let noteContent: string = '';
  if (editClientOutsideMetro.value) {
    noteContent =
      'Client outside metro rate changed from ' +
      props.jobAccountingDetails.clientRates[0].outsideMetroRate +
      '% to ' +
      outsideMetroEditRate.value +
      '%';

    props.jobAccountingDetails.clientRates[0].outsideMetroRate = JSON.parse(
      JSON.stringify(outsideMetroEditRate.value),
    );
    const toBeApplied =
      outsideMetroEditRate.value !== null && outsideMetroEditRate.value > 0
        ? true
        : false;
    emit('updateForceOutsideMetroChargeClient', toBeApplied);
  } else if (editDriverOutsideMetro.value) {
    noteContent =
      'Driver outside metro rate changed from ' +
      props.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate +
      '% to ' +
      outsideMetroEditRate.value +
      '%';

    props.jobAccountingDetails.fleetAssetRates[0].outsideMetroRate = JSON.parse(
      JSON.stringify(outsideMetroEditRate.value),
    );

    const toBeApplied = outsideMetroEditRate.value ? true : false;
    emit('updateForceOutsideMetroChargeFleetAsset', toBeApplied);
  }

  const note: Communication | any = {
    id: '',
    epoch: moment().valueOf(),
    user: sessionManager.getUserName(),
    type: {
      id: 0,
      communicationDetails: {
        visibleTo: [2, 5],
      },
    },
    body: noteContent,
    hidden: false,
  };

  props.newJobNotes.push(note);

  outsideMetroEditRate.value = null;
  editDriverOutsideMetro.value = false;
  editClientOutsideMetro.value = false;
  outsideMetroEditLabel.value = '';
}

function cancelOutsideMetroEdit() {
  outsideMetroEditRate.value = null;
  editDriverOutsideMetro.value = false;
  editClientOutsideMetro.value = false;
  outsideMetroEditLabel.value = '';
}

const requiredBreakForJobLength: ComputedRef<number> = computed(() => {
  if (!props.jobAccountingDetails) {
    return 0;
  }
  if (props.entityType === RateEntityType.CLIENT) {
    return 0;
  }
  return props.jobAccountingDetails.requiredBreakForJobLength
    ? props.jobAccountingDetails.requiredBreakForJobLength
    : 0;
});

const breakDurationBreakdown: ComputedRef<string> = computed(() => {
  if (props.entityType === RateEntityType.FLEET_ASSET) {
    const breakDur =
      props.jobAccountingDetails.fleetAssetRates[0].breakDuration.breakSummaryList?.reduce(
        (total, curr) => total + curr.durationInMilliseconds,
        0,
      ) ?? 0;
    const paidBreakDur =
      props.jobAccountingDetails.fleetAssetRates[0].breakDuration.breakSummaryList?.reduce(
        (total, curr) =>
          total + (curr.payFleetAsset ? curr.durationInMilliseconds : 0),
        0,
      ) ?? 0;
    if (paidBreakDur === 0) {
      return '';
    } else {
      return `Total Break Taken (${returnDurationFromMilliseconds(
        breakDur,
      )}) less Paid Break Duration (${returnDurationFromMilliseconds(
        paidBreakDur,
      )})`;
    }
  }
  if (props.entityType === 'CLIENT') {
    const breakDur =
      props.jobAccountingDetails.clientRates[0].breakDuration.breakSummaryList?.reduce(
        (total, curr) => total + curr.durationInMilliseconds,
        0,
      ) ?? 0;
    const chargedBreakDur =
      props.jobAccountingDetails.clientRates[0].breakDuration.breakSummaryList?.reduce(
        (total, curr) =>
          total + (curr.chargeClient ? curr.durationInMilliseconds : 0),
        0,
      ) ?? 0;
    if (chargedBreakDur === 0) {
      return '';
    } else {
      return `Total Break Taken (${returnDurationFromMilliseconds(
        breakDur,
      )}) less Charged Break Duration (${returnDurationFromMilliseconds(
        chargedBreakDur,
      )})`;
    }
  }
  return '';
});

/**
 * Returns a true if the breaks taken by the drivers satisfy the minimum
 * required duration, or false if not
 */
const requiredBreakDurationsMet: ComputedRef<boolean> = computed(() => {
  if (
    !props.jobAccountingDetails ||
    !props.jobAccountingDetails.clientRates[0] ||
    !props.jobAccountingDetails.fleetAssetRates[0]
  ) {
    return false;
  }
  if (requiredBreakForJobLength.value === 0) {
    return true;
  }
  if (props.entityType === RateEntityType.FLEET_ASSET) {
    const breakDur =
      props.jobAccountingDetails.fleetAssetRates[0].breakDuration.breakSummaryList.reduce(
        (total, curr) => total + curr.durationInMilliseconds,
        0,
      );
    return breakDur >= requiredBreakForJobLength.value;
  }
  return false;
});

const currentBreakDuration: ComputedRef<string> = computed(() => {
  if (
    !props.jobAccountingDetails ||
    !props.jobAccountingDetails.clientRates[0] ||
    !props.jobAccountingDetails.fleetAssetRates[0]
  ) {
    return '';
  }

  let isTimeRate = false;
  if (props.entityType === 'CLIENT') {
    isTimeRate =
      props.jobAccountingDetails.clientRates[0].rate.rateTypeId === 1;
  }
  if (props.entityType === RateEntityType.FLEET_ASSET) {
    isTimeRate =
      props.jobAccountingDetails.fleetAssetRates[0].rate.rateTypeId === 1;
  }
  const otherBreakTypeName = isTimeRate ? 'Standby' : 'Demur.';

  if (props.entityType === 'CLIENT') {
    const breakDur = props.jobAccountingDetails.clientRates[0].breakDuration;
    const otherBreaks = breakDur.readableOtherDuration
      ? `(${
          breakDur.readableFreightDuration
            ? breakDur.readableFreightDuration
            : '0m'
        }
        + ${breakDur.readableOtherDuration} during ${otherBreakTypeName})`
      : '';
    return `${breakDur.readableTotalDuration} ${otherBreaks}`.trim();
  } else if (props.entityType === RateEntityType.FLEET_ASSET) {
    const breakDur =
      props.jobAccountingDetails.fleetAssetRates[0].breakDuration;
    const otherBreaks = breakDur.readableOtherDuration
      ? `(${
          breakDur.readableFreightDuration
            ? breakDur.readableFreightDuration
            : '0m'
        }
      + ${breakDur.readableOtherDuration} during ${otherBreakTypeName})`
      : '';
    return `${breakDur.readableTotalDuration} ${otherBreaks}`.trim();
  } else {
    return '';
  }
});

/**
 * Returns a filtered list of additional charges based on the entity type.
 */
const filteredAdditionalCharges = computed(() => {
  if (props.entityType === RateEntityType.FLEET_ASSET) {
    return additionalChargeList.value.filter(
      (item) => !!item.fleetAsset.charge,
    );
  }
  return additionalChargeList.value;
});

/**
 * Returns the rate config for the provided additional charge based on the entity type.
 * @param item - The additional charge item to get the configuration for.
 * @returns The AdditionalChargeRateConfig for the given item.
 */
function chargeConfig(item: AdditionalChargeItem): AdditionalChargeRateConfig {
  return props.entityType === RateEntityType.FLEET_ASSET
    ? item.fleetAsset
    : item.client;
}

/**
 * Returns either client or fleet fuel surcharge (from props) based on the entity type.
 */
const fuelSurcharge: ComputedRef<ClientFuelSurchargeRate> = computed(() => {
  return props.entityType === RateEntityType.FLEET_ASSET
    ? props.fleetAssetFuelSurcharge
    : props.clientFuelSurcharge;
});

/**
 * Determines if the "Outside Metro" elements should be shown based on whether
 * the entity's rate type is TIME
 */
const showOutsideMetro: ComputedRef<boolean> = computed(() => {
  if (props.entityType === RateEntityType.FLEET_ASSET) {
    return (
      props.jobAccountingDetails?.fleetAssetRates?.[0]?.rate?.rateTypeId ===
      JobRateType.TIME
    );
  }
  return (
    props.clientFuelSurcharge !== undefined &&
    props.jobAccountingDetails?.clientRates?.[0]?.rate?.rateTypeId ===
      JobRateType.TIME
  );
});

/**
 * Gets and sets the appropriate outside metro property based on the entity
 * type. Used in the template for the checkbox to toggle the outside metro charge.
 */
const outsideMetroModel = computed({
  get() {
    return props.entityType === RateEntityType.FLEET_ASSET
      ? isForceOutsideMetroChargeFleetAsset.value
      : isForceOutsideMetroChargeClient.value;
  },
  set(val: boolean) {
    if (props.entityType === RateEntityType.FLEET_ASSET) {
      isForceOutsideMetroChargeFleetAsset.value = val;
    } else {
      isForceOutsideMetroChargeClient.value = val;
    }
  },
});

/**
 * Returns the outside metro rate based on the entity type.
 * If the entity type is FLEET_ASSET, it returns the fleet asset's outside metro rate,
 * otherwise it returns the client's outside metro rate.
 */
const outsideMetroRate: ComputedRef<number | null> = computed(() => {
  if (props.entityType === RateEntityType.FLEET_ASSET) {
    return props.jobAccountingDetails.fleetAssetRates[0]?.outsideMetroRate;
  }
  return props.jobAccountingDetails.clientRates[0]?.outsideMetroRate;
});

/**
 * Opens the break duration dialog by emitting an event to the parent component.
 */
function openBreakDurationDialog() {
  emit('setViewingBreakDurationDialog', true);
}

const tollAdminAndHandlingId: ComputedRef<string | undefined> = computed(() => {
  return useRootStore().tollAdminAndHandlingId;
});

/**
 * Returns a formatted string representing the applied additional charge value
 * @param item - The AdditionalChargeItem to format
 * @param type - The RateEntityType to determine which rate configuration to use
 */
function appliedAdditionalCharge(
  item: AdditionalChargeItem,
  type: RateEntityType,
): string {
  const rateConfig =
    type === RateEntityType.CLIENT ? item.client : item.fleetAsset;
  const isTollAdminAndServiceFee = item._id === tollAdminAndHandlingId.value;
  const isDollar =
    rateConfig.chargeBasis === AdditionalChargeRateBasis.FIXED ||
    isTollAdminAndServiceFee;

  const isPositive = Math.sign(rateConfig.charge) > 0;
  const absoluteRate = Math.abs(rateConfig.charge);

  let rateResult: string = '';

  if (!isPositive && rateConfig.charge !== 0) {
    rateResult += '- ';
  }
  if (isDollar) {
    rateResult += '$' + absoluteRate.toFixed(2);
  } else {
    rateResult += absoluteRate + '%';
  }

  return rateResult;
}

/**
 * Called from the template when pressing the "Add New Charge" button. Emits an
 * event to the parent component to open the AddAdditionalCharge dialog
 * component
 * @param value - A boolean value indicating whether to open the dialog or not
 */
function addNewCharge(value: boolean) {
  emit('addNewAdditionalCharge', value);
}

/**
 * Emits an event to update an additional charge item. The event is picked up by
 * the parent component to update the additional charge item.
 */
function updateAdditionalChargeItem(update: UpdateAdditionalChargeItem) {
  const isValid = validateAdditionalChargeUpdate(update);

  // If the update is not valid, we reset the quantity to its existing value
  // to prevent an empty input
  if (!isValid) {
    const foundCharge = additionalChargeList.value.find(
      (item) => item._id === update.id,
    );
    if (foundCharge?.quantity) {
      update.type = AdditionalChargeUpdateType.SET_QUANTITY;
      update.quantity = foundCharge.quantity;
    } else {
      showNotification('The additional charge could not be updated.', {
        title: 'Job Pricing - Additional Charges',
      });
      return;
    }
  }
  emit('updateAdditionalCharge', update);
}
</script>

<style scoped lang="scss">
.current-applied-charges {
  height: 100%;
  .header-section {
    background-color: #393939;
    color: #e48e1d;
    font-weight: 500;
    font-size: $font-size-large;
    text-transform: uppercase;
  }

  .header-text {
    color: rgb(128, 128, 128);
    font-weight: 500;
    font-size: 1.2em;
    text-transform: uppercase;
  }

  .bottom-sheet-fixed {
    width: 100%;
    position: absolute;
    background-color: $border-dark;
    bottom: 0px;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
  }

  .value-primary {
    font-size: $font-size-large;
    font-weight: 300;
    letter-spacing: 0.02em;
    color: white;
  }

  .value-secondary {
    font-size: $font-size-small;
    font-weight: 600;
    letter-spacing: 0.01em;
    color: #858585;
  }

  .label {
    font-size: 1em;
    font-weight: 400;
    color: rgb(172, 172, 172);
  }

  .charge-card__subcharge {
    cursor: pointer;
    border-bottom: 1px solid rgb(94, 94, 94) !important;
    &:hover {
      background-color: rgb(63, 63, 63) !important;
    }

    transition:
      background-color 0.2s,
      border-left 0.2s;

    .value-primary {
      font-size: $font-size-medium;
      font-weight: 400;
      letter-spacing: 0.02em;
      color: $bg-light;
      white-space: nowrap;
    }

    .value-secondary {
      font-size: $font-size-small;
      font-weight: 600;
      letter-spacing: 0.01em;
      color: #858585;
      white-space: nowrap;
    }

    .label {
      font-size: 1em;
      font-weight: 400;
      color: rgb(172, 172, 172);
    }
  }

  .quantity-container {
    max-width: 110px;

    .quantity-text {
      font-size: $font-size-11;
      padding-right: 5px;
      font-weight: 300;
      line-height: 19px;
    }

    .quantity-amount {
      font-size: $font-size-13;
      padding-right: 5px;
    }

    .quantity-down {
      position: relative;
      top: -2px;
      &:hover {
        color: #2196f3;
        transition: 0.1s;
      }
    }

    .quantity-up {
      position: relative;
      top: 2px;
      &:hover {
        color: #2196f3;
        transition: 0.1s;
      }
    }
  }

  .expansion-panel-container {
    .expansion-item-container {
      border-radius: $border-radius-sm !important;
      background-color: var(--background-color-300) !important;

      .lineitem__label {
        font-size: $font-size-12;
        text-transform: uppercase;
        font-weight: 600;
        color: var(--light-text-color);
      }
      .lineitem__value {
        font-size: $font-size-14;
        font-weight: 400;

        &:last-child {
          white-space: nowrap;
        }
      }
    }
  }

  .applied-charges-table {
    border-radius: $border-radius-sm;
    background-color: var(--background-color-300);
    border-collapse: collapse;
    border-color: var(--background-color-300);
    width: 100%;

    tr {
      border-bottom: 1px solid $app-dark-primary-200;

      &:last-child {
        border-bottom: none;
      }
    }

    td {
      // border: 1px solid $app-dark-primary-200;
      padding: 8px;
      text-align: right;

      &.reduce-padding {
        padding: 2px;
      }
    }

    .lineitem__label {
      font-size: $font-size-12;
      text-transform: uppercase;
      font-weight: 600;
      color: var(--light-text-color);
      text-align: left;
    }
    .lineitem__value {
      font-size: $font-size-14;
      font-weight: 400;
      color: var(--text-color);
    }
  }

  .charge__table {
    .charge__table--row {
      background-color: #303030;
      &.header {
        background-color: transparent;
      }
      &.light {
        background-color: $client-hover;
      }
      .charge__table--cell {
        padding: 12px 0px;
        text-align: center;
        font-size: 1.05em;
        font-weight: 400;
        border-bottom: 1px solid #423d3c;
        .header {
          font-weight: 600;
          font-size: 0.7em;
          text-transform: uppercase;
          color: rgb(105, 105, 105);
        }
        .value {
          font-weight: 400;
          font-size: $font-size-large;
          color: rgb(255, 123, 0);
        }
      }
    }
  }
}

.edit-break-icon {
  margin-left: 14px;
  border-radius: 100px;
  background-color: var(--background-color-100);
  color: $warning !important;
  transition: all 0.3s;

  &:hover {
    scale: 1.4;
  }
}
</style>
